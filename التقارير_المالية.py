from الإعدادات_العامة import *
from ستايل import *
from أزرار_الواجهة import *
from قاعدة_البيانات import *
from تنسيق_التقارير_المالية import apply_financial_reports_style
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import seaborn as sns
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

# تعيين الخط العربي لـ matplotlib
import matplotlib.font_manager as fm

# البحث عن خط عربي متاح
arabic_fonts = []
for font in fm.fontManager.ttflist:
    if any(name in font.name.lower() for name in ['arabic', 'tahoma', 'arial']):
        arabic_fonts.append(font.name)

# تعيين الخط العربي
if arabic_fonts:
    plt.rcParams['font.family'] = arabic_fonts[0]
else:
    # خطوط احتياطية
    plt.rcParams['font.family'] = ['Tahoma', 'Arial Unicode MS', 'DejaVu Sans', 'sans-serif']

# إعدادات إضافية للعربية
plt.rcParams['axes.unicode_minus'] = False

class FinancialReportsWindow(QDialog):
    """نافذة التقارير المالية الاحترافية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setup_window()
        self.create_ui()
        self.load_initial_data()
        apply_stylesheet(self)
        apply_financial_reports_style(self)
        
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.setWindowTitle("التقارير المالية - منظومة المهندس")
        self.setGeometry(100, 100, 1600, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(False)
        
        # إعداد أيقونة النافذة
        icon_path = os.path.join(icons_dir, 'تقارير_مالية.png')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_layout)
        
        # المحتوى الرئيسي
        content_layout = QHBoxLayout()
        
        # اللوحة اليسرى - الفلاتر والإعدادات
        self.create_filters_panel(content_layout)
        
        # اللوحة الرئيسية - التبويبات والتقارير
        self.create_main_panel(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # شريط الحالة
        self.create_status_bar(main_layout)
    
    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_frame = QFrame()
        toolbar_frame.setObjectName("ToolbarFrame")
        toolbar_frame.setFixedHeight(60)
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # عنوان النافذة
        title_label = QLabel("📊 التقارير المالية")
        title_label.setObjectName("TitleLabel")
        title_label.setStyleSheet("""
            QLabel#TitleLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار الأدوات
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setObjectName("ToolbarButton")
        self.refresh_btn.clicked.connect(self.refresh_data)
        
        self.export_btn = QPushButton("📤 تصدير")
        self.export_btn.setObjectName("ToolbarButton")
        self.export_btn.clicked.connect(self.export_report)
        
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setObjectName("ToolbarButton")
        self.print_btn.clicked.connect(self.print_report)
        
        self.settings_btn = QPushButton("⚙️ إعدادات")
        self.settings_btn.setObjectName("ToolbarButton")
        self.settings_btn.clicked.connect(self.open_settings)
        
        for btn in [self.refresh_btn, self.export_btn, self.print_btn, self.settings_btn]:
            btn.setFixedSize(100, 40)
            toolbar_layout.addWidget(btn)
        
        parent_layout.addWidget(toolbar_frame)
    
    def create_filters_panel(self, parent_layout):
        """إنشاء لوحة الفلاتر والإعدادات"""
        filters_frame = QFrame()
        filters_frame.setObjectName("FiltersFrame")
        filters_frame.setFixedWidth(300)
        filters_layout = QVBoxLayout(filters_frame)
        filters_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان اللوحة
        filters_title = QLabel("🔍 الفلاتر والإعدادات")
        filters_title.setObjectName("SectionTitle")
        filters_title.setStyleSheet("""
            QLabel#SectionTitle {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        filters_layout.addWidget(filters_title)
        
        # فلتر السنة
        year_group = QGroupBox("السنة المالية")
        year_layout = QVBoxLayout(year_group)
        
        self.year_combo = QComboBox()
        self.populate_years()
        self.year_combo.currentTextChanged.connect(self.on_year_changed)
        year_layout.addWidget(self.year_combo)
        
        filters_layout.addWidget(year_group)
        
        # فلتر الفترة الزمنية
        period_group = QGroupBox("الفترة الزمنية")
        period_layout = QVBoxLayout(period_group)
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "السنة كاملة", "الربع الأول", "الربع الثاني", 
            "الربع الثالث", "الربع الرابع", "فترة مخصصة"
        ])
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        period_layout.addWidget(self.period_combo)
        
        # تواريخ مخصصة
        self.custom_dates_widget = QWidget()
        custom_layout = QVBoxLayout(self.custom_dates_widget)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-365))
        self.start_date.setCalendarPopup(True)
        custom_layout.addWidget(QLabel("من تاريخ:"))
        custom_layout.addWidget(self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        custom_layout.addWidget(QLabel("إلى تاريخ:"))
        custom_layout.addWidget(self.end_date)
        
        self.custom_dates_widget.setVisible(False)
        period_layout.addWidget(self.custom_dates_widget)
        
        filters_layout.addWidget(period_group)
        
        # فلتر نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QVBoxLayout(report_type_group)
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير شامل", "الإيرادات فقط", "المصروفات فقط", 
            "المشاريع", "العملاء", "الموظفين"
        ])
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        report_type_layout.addWidget(self.report_type_combo)
        
        filters_layout.addWidget(report_type_group)
        
        # زر تطبيق الفلاتر
        apply_filters_btn = QPushButton("✅ تطبيق الفلاتر")
        apply_filters_btn.setObjectName("ApplyButton")
        apply_filters_btn.clicked.connect(self.apply_filters)
        apply_filters_btn.setFixedHeight(40)
        filters_layout.addWidget(apply_filters_btn)
        
        filters_layout.addStretch()
        
        parent_layout.addWidget(filters_frame)
    
    def create_main_panel(self, parent_layout):
        """إنشاء اللوحة الرئيسية للتقارير"""
        main_frame = QFrame()
        main_frame.setObjectName("MainFrame")
        main_layout = QVBoxLayout(main_frame)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("ReportsTabWidget")
        
        # تبويب الملخص المالي
        self.create_summary_tab()
        
        # تبويب الرسوم البيانية
        self.create_charts_tab()
        
        # تبويب التقارير التفصيلية
        self.create_detailed_reports_tab()
        
        # تبويب المقارنات
        self.create_comparison_tab()
        
        main_layout.addWidget(self.tab_widget)
        parent_layout.addWidget(main_frame)
    
    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setObjectName("StatusFrame")
        status_frame.setFixedHeight(30)
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)
        
        self.status_label = QLabel("جاهز")
        self.status_label.setObjectName("StatusLabel")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.last_update_label = QLabel(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        self.last_update_label.setObjectName("UpdateLabel")
        status_layout.addWidget(self.last_update_label)
        
        parent_layout.addWidget(status_frame)

    def create_summary_tab(self):
        """إنشاء تبويب الملخص المالي"""
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)
        summary_layout.setContentsMargins(15, 15, 15, 15)

        # بطاقات الإحصائيات الرئيسية
        cards_layout = QHBoxLayout()

        # بطاقة الإيرادات
        self.revenue_card = self.create_stat_card("💰 إجمالي الإيرادات", "0 د.ل", "#27ae60")
        cards_layout.addWidget(self.revenue_card)

        # بطاقة المصروفات
        self.expenses_card = self.create_stat_card("💸 إجمالي المصروفات", "0 د.ل", "#e74c3c")
        cards_layout.addWidget(self.expenses_card)

        # بطاقة صافي الربح
        self.profit_card = self.create_stat_card("📈 صافي الربح", "0 د.ل", "#3498db")
        cards_layout.addWidget(self.profit_card)

        # بطاقة المبالغ المستحقة
        self.pending_card = self.create_stat_card("⏳ المبالغ المستحقة", "0 د.ل", "#f39c12")
        cards_layout.addWidget(self.pending_card)

        summary_layout.addLayout(cards_layout)

        # جدول الملخص التفصيلي
        summary_table_frame = QFrame()
        summary_table_frame.setObjectName("TableFrame")
        summary_table_layout = QVBoxLayout(summary_table_frame)

        table_title = QLabel("📋 الملخص التفصيلي")
        table_title.setObjectName("TableTitle")
        table_title.setStyleSheet("""
            QLabel#TableTitle {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        summary_table_layout.addWidget(table_title)

        self.summary_table = QTableWidget()
        self.summary_table.setObjectName("SummaryTable")
        self.setup_summary_table()
        summary_table_layout.addWidget(self.summary_table)

        summary_layout.addWidget(summary_table_frame)

        self.tab_widget.addTab(summary_tab, "📊 الملخص المالي")

    def create_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        charts_tab = QWidget()
        charts_layout = QVBoxLayout(charts_tab)
        charts_layout.setContentsMargins(15, 15, 15, 15)

        # شريط أدوات الرسوم البيانية
        charts_toolbar = QHBoxLayout()

        chart_type_label = QLabel("نوع الرسم البياني:")
        charts_toolbar.addWidget(chart_type_label)

        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "أعمدة بيانية", "رسم دائري", "رسم خطي", "رسم مساحي"
        ])
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        charts_toolbar.addWidget(self.chart_type_combo)

        charts_toolbar.addStretch()

        save_chart_btn = QPushButton("💾 حفظ الرسم")
        save_chart_btn.clicked.connect(self.save_chart)
        charts_toolbar.addWidget(save_chart_btn)

        charts_layout.addLayout(charts_toolbar)

        # منطقة الرسوم البيانية
        self.chart_widget = QWidget()
        self.chart_layout = QVBoxLayout(self.chart_widget)

        # إنشاء الرسم البياني الافتراضي
        self.create_default_chart()

        charts_layout.addWidget(self.chart_widget)

        self.tab_widget.addTab(charts_tab, "📈 الرسوم البيانية")

    def create_detailed_reports_tab(self):
        """إنشاء تبويب التقارير التفصيلية"""
        detailed_tab = QWidget()
        detailed_layout = QVBoxLayout(detailed_tab)
        detailed_layout.setContentsMargins(15, 15, 15, 15)

        # شريط أدوات التقارير التفصيلية
        detailed_toolbar = QHBoxLayout()

        report_detail_label = QLabel("نوع التقرير التفصيلي:")
        detailed_toolbar.addWidget(report_detail_label)

        self.detailed_type_combo = QComboBox()
        self.detailed_type_combo.addItems([
            "تقرير المشاريع المالي", "تقرير العملاء المالي",
            "تقرير الموظفين المالي", "تقرير التدفق النقدي",
            "تقرير الأرباح والخسائر", "تقرير الميزانية العمومية",
            "تقرير تحليل الأداء", "تقرير المصروفات التفصيلي",
            "تقرير العقارات المالي", "تقرير التدريب المالي"
        ])
        self.detailed_type_combo.currentTextChanged.connect(self.load_detailed_report)
        detailed_toolbar.addWidget(self.detailed_type_combo)

        # فلاتر إضافية للتقارير
        self.create_detailed_filters(detailed_toolbar)

        detailed_toolbar.addStretch()

        # أزرار الأدوات
        print_detailed_btn = QPushButton("🖨️ طباعة")
        print_detailed_btn.clicked.connect(self.print_detailed_report)
        detailed_toolbar.addWidget(print_detailed_btn)

        export_detailed_btn = QPushButton("📤 تصدير")
        export_detailed_btn.clicked.connect(self.export_detailed_report)
        detailed_toolbar.addWidget(export_detailed_btn)

        refresh_detailed_btn = QPushButton("🔄 تحديث")
        refresh_detailed_btn.clicked.connect(self.load_detailed_report)
        detailed_toolbar.addWidget(refresh_detailed_btn)

        detailed_layout.addLayout(detailed_toolbar)

        # إحصائيات التقرير التفصيلي
        self.create_detailed_stats_panel(detailed_layout)

        # جدول التقرير التفصيلي
        self.detailed_table = QTableWidget()
        self.detailed_table.setObjectName("DetailedTable")
        self.setup_detailed_table()
        detailed_layout.addWidget(self.detailed_table)

        self.tab_widget.addTab(detailed_tab, "📋 التقارير التفصيلية")

    def create_detailed_filters(self, parent_layout):
        """إنشاء فلاتر التقارير التفصيلية"""
        # فلتر الفترة الزمنية للتقرير التفصيلي
        period_label = QLabel("الفترة:")
        parent_layout.addWidget(period_label)

        self.detailed_period_combo = QComboBox()
        self.detailed_period_combo.addItems([
            "السنة كاملة", "الربع الأول", "الربع الثاني",
            "الربع الثالث", "الربع الرابع", "الشهر الحالي",
            "الشهر السابق", "آخر 3 أشهر", "آخر 6 أشهر"
        ])
        self.detailed_period_combo.currentTextChanged.connect(self.load_detailed_report)
        parent_layout.addWidget(self.detailed_period_combo)

        # فلتر الحالة
        status_label = QLabel("الحالة:")
        parent_layout.addWidget(status_label)

        self.detailed_status_combo = QComboBox()
        self.detailed_status_combo.addItems([
            "الكل", "نشط", "مكتمل", "معلق", "ملغي"
        ])
        self.detailed_status_combo.currentTextChanged.connect(self.load_detailed_report)
        parent_layout.addWidget(self.detailed_status_combo)

    def create_detailed_stats_panel(self, parent_layout):
        """إنشاء لوحة إحصائيات التقرير التفصيلي"""
        stats_frame = QFrame()
        stats_frame.setObjectName("DetailedStatsFrame")
        stats_frame.setFixedHeight(80)
        stats_layout = QHBoxLayout(stats_frame)

        # إحصائيات ديناميكية تتغير حسب نوع التقرير
        self.detailed_stats_labels = {}

        for i in range(4):
            stat_widget = QWidget()
            stat_layout = QVBoxLayout(stat_widget)
            stat_layout.setAlignment(Qt.AlignCenter)

            title_label = QLabel("--")
            title_label.setObjectName("DetailedStatTitle")
            title_label.setAlignment(Qt.AlignCenter)

            value_label = QLabel("0")
            value_label.setObjectName("DetailedStatValue")
            value_label.setAlignment(Qt.AlignCenter)

            stat_layout.addWidget(title_label)
            stat_layout.addWidget(value_label)

            self.detailed_stats_labels[f"stat_{i}"] = {
                "title": title_label,
                "value": value_label
            }

            stats_layout.addWidget(stat_widget)

        parent_layout.addWidget(stats_frame)

    def setup_detailed_table(self):
        """إعداد جدول التقرير التفصيلي"""
        # إعدادات أساسية للجدول
        self.detailed_table.setAlternatingRowColors(True)
        self.detailed_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.detailed_table.setSortingEnabled(True)
        self.detailed_table.horizontalHeader().setStretchLastSection(True)

    def create_comparison_tab(self):
        """إنشاء تبويب المقارنات"""
        comparison_tab = QWidget()
        comparison_layout = QVBoxLayout(comparison_tab)
        comparison_layout.setContentsMargins(15, 15, 15, 15)

        # إعدادات المقارنة
        comparison_settings = QHBoxLayout()

        # السنة الأولى
        year1_group = QGroupBox("السنة الأولى")
        year1_layout = QVBoxLayout(year1_group)
        self.year1_combo = QComboBox()
        self.populate_years_combo(self.year1_combo)
        year1_layout.addWidget(self.year1_combo)
        comparison_settings.addWidget(year1_group)

        # السنة الثانية
        year2_group = QGroupBox("السنة الثانية")
        year2_layout = QVBoxLayout(year2_group)
        self.year2_combo = QComboBox()
        self.populate_years_combo(self.year2_combo)
        year2_layout.addWidget(self.year2_combo)
        comparison_settings.addWidget(year2_group)

        # زر المقارنة
        compare_btn = QPushButton("🔄 مقارنة")
        compare_btn.setFixedSize(100, 60)
        compare_btn.clicked.connect(self.perform_comparison)
        comparison_settings.addWidget(compare_btn)

        comparison_layout.addLayout(comparison_settings)

        # نتائج المقارنة
        self.comparison_results = QWidget()
        self.comparison_layout = QVBoxLayout(self.comparison_results)
        comparison_layout.addWidget(self.comparison_results)

        self.tab_widget.addTab(comparison_tab, "⚖️ المقارنات")

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("StatCard")
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QFrame#StatCard {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                margin: 5px;
            }}
            QLabel#StatTitle {{
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }}
            QLabel#StatValue {{
                font-size: 20px;
                font-weight: bold;
                color: {color};
                padding: 5px;
            }}
        """)

        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)

        title_label = QLabel(title)
        title_label.setObjectName("StatTitle")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setObjectName("StatValue")
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)

        return card

    def setup_summary_table(self):
        """إعداد جدول الملخص"""
        headers = ["البيان", "القيمة", "النسبة المئوية", "الملاحظات"]
        self.summary_table.setColumnCount(len(headers))
        self.summary_table.setHorizontalHeaderLabels(headers)

        # تنسيق الجدول
        self.summary_table.horizontalHeader().setStretchLastSection(True)
        self.summary_table.setAlternatingRowColors(True)
        self.summary_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # تعيين عرض الأعمدة
        self.summary_table.setColumnWidth(0, 200)
        self.summary_table.setColumnWidth(1, 150)
        self.summary_table.setColumnWidth(2, 120)

    def populate_years(self):
        """تعبئة قائمة السنوات"""
        try:
            current_year = QDate.currentDate().year()
            years = []

            # إضافة السنوات من 2020 إلى السنة الحالية + 2
            for year in range(2020, current_year + 3):
                years.append(str(year))

            self.year_combo.addItems(years)
            self.year_combo.setCurrentText(str(current_year))

        except Exception as e:
            print(f"خطأ في تعبئة السنوات: {e}")

    def populate_years_combo(self, combo):
        """تعبئة قائمة السنوات لكومبو بوكس محدد"""
        try:
            current_year = QDate.currentDate().year()
            years = []

            for year in range(2020, current_year + 3):
                years.append(str(year))

            combo.addItems(years)
            combo.setCurrentText(str(current_year))

        except Exception as e:
            print(f"خطأ في تعبئة السنوات: {e}")

    def create_default_chart(self):
        """إنشاء الرسم البياني الافتراضي"""
        try:
            # إنشاء figure و canvas
            self.figure = Figure(figsize=(12, 6), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            self.chart_layout.addWidget(self.canvas)

            # إنشاء رسم بياني افتراضي
            self.update_chart()

        except Exception as e:
            print(f"خطأ في إنشاء الرسم البياني: {e}")

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.update_status("جاري تحميل البيانات...")
        try:
            # تهيئة المتغيرات بقيم افتراضية
            self.total_revenue = 0
            self.total_expenses = 0
            self.total_pending = 0
            self.net_profit = 0

            self.load_financial_data()
            self.update_summary_cards()
            self.load_summary_table()
            self.update_chart()
            self.update_status("تم تحميل البيانات بنجاح")
        except Exception as e:
            self.update_status(f"خطأ في تحميل البيانات: {e}")
            # تأكد من وجود قيم افتراضية حتى في حالة الخطأ
            if not hasattr(self, 'total_revenue'):
                self.total_revenue = 0
                self.total_expenses = 0
                self.total_pending = 0
                self.net_profit = 0

    def load_financial_data(self):
        """تحميل البيانات المالية من قاعدة البيانات"""
        try:
            selected_year = self.year_combo.currentText()

            # الحصول على اتصال قاعدة البيانات
            conn = self.get_db_connection(selected_year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # جلب بيانات الإيرادات (دفعات المشاريع)
            # أولاً، التحقق من وجود الجدول والأعمدة
            cursor.execute("SHOW TABLES LIKE 'المشاريع_المدفوعات'")
            table_exists = cursor.fetchone()

            if table_exists:
                cursor.execute("DESCRIBE المشاريع_المدفوعات")
                columns = cursor.fetchall()
                print(f"أعمدة جدول المشاريع_المدفوعات: {[col['Field'] for col in columns]}")

                # محاولة جلب البيانات بطرق مختلفة
                try:
                    cursor.execute("""
                        SELECT SUM(المبلغ_المدفوع) as total_revenue
                        FROM المشاريع_المدفوعات
                        WHERE YEAR(تاريخ_الدفع) = %s
                    """, (selected_year,))
                    revenue_result = cursor.fetchone()
                    self.total_revenue = float(revenue_result['total_revenue'] or 0)

                    # إذا لم توجد بيانات للسنة المحددة، جلب جميع البيانات
                    if self.total_revenue == 0:
                        cursor.execute("SELECT SUM(المبلغ_المدفوع) as total_revenue FROM المشاريع_المدفوعات")
                        revenue_result = cursor.fetchone()
                        self.total_revenue = float(revenue_result['total_revenue'] or 0)

                        # إذا ما زالت النتيجة صفر، جرب جلب البيانات من جدول المشاريع
                        if self.total_revenue == 0:
                            cursor.execute("SELECT SUM(المدفوع) as total_revenue FROM المشاريع")
                            revenue_result = cursor.fetchone()
                            self.total_revenue = float(revenue_result['total_revenue'] or 0)
                            print(f"تم جلب الإيرادات من جدول المشاريع: {self.total_revenue}")

                except Exception as e:
                    print(f"خطأ في جلب الإيرادات: {e}")
                    # محاولة جلب من جدول المشاريع كبديل
                    cursor.execute("SELECT SUM(المدفوع) as total_revenue FROM المشاريع")
                    revenue_result = cursor.fetchone()
                    self.total_revenue = float(revenue_result['total_revenue'] or 0)
            else:
                print("جدول المشاريع_المدفوعات غير موجود، جلب البيانات من جدول المشاريع")
                cursor.execute("SELECT SUM(المدفوع) as total_revenue FROM المشاريع")
                revenue_result = cursor.fetchone()
                self.total_revenue = float(revenue_result['total_revenue'] or 0)

            # جلب بيانات المصروفات
            try:
                cursor.execute("""
                    SELECT SUM(المبلغ) as total_expenses
                    FROM الحسابات
                    WHERE YEAR(تاريخ_المصروف) = %s
                """, (selected_year,))
                expenses_result = cursor.fetchone()
                self.total_expenses = float(expenses_result['total_expenses'] or 0)

                # إذا لم توجد بيانات للسنة المحددة، جلب جميع البيانات
                if self.total_expenses == 0:
                    cursor.execute("SELECT SUM(المبلغ) as total_expenses FROM الحسابات")
                    expenses_result = cursor.fetchone()
                    self.total_expenses = float(expenses_result['total_expenses'] or 0)
            except Exception as e:
                print(f"خطأ في جلب المصروفات: {e}")
                # محاولة بأسماء أعمدة مختلفة
                try:
                    cursor.execute("SELECT SUM(المبلغ) as total_expenses FROM الحسابات WHERE YEAR(تاريخ_المصروف) = %s", (selected_year,))
                    expenses_result = cursor.fetchone()
                    self.total_expenses = float(expenses_result['total_expenses'] or 0)
                except:
                    cursor.execute("SELECT SUM(المبلغ) as total_expenses FROM الحسابات")
                    expenses_result = cursor.fetchone()
                    self.total_expenses = float(expenses_result['total_expenses'] or 0)

            # جلب المبالغ المستحقة (الباقي من المشاريع)
            cursor.execute("""
                SELECT SUM(الباقي) as total_pending
                FROM المشاريع
                WHEREالحالة!= 'تم التسليم'
            """)
            pending_result = cursor.fetchone()
            self.total_pending = float(pending_result['total_pending'] or 0)

            # حساب صافي الربح
            self.net_profit = self.total_revenue - self.total_expenses

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البيانات المالية: {e}")
            # استخدام بيانات تجريبية
            self.load_demo_data()

        # إذا لم توجد بيانات كافية، استخدم بيانات تجريبية
        if self.total_revenue == 0:
            print("لا توجد إيرادات في قاعدة البيانات، سيتم استخدام بيانات تجريبية")
            self.load_demo_data()

        # طباعة البيانات للتشخيص
        print(f"البيانات المحملة - الإيرادات: {self.total_revenue}, المصروفات: {self.total_expenses}, الباقي: {self.total_pending}, صافي الربح: {self.net_profit}")

    def load_demo_data(self):
        """تحميل بيانات تجريبية للعرض"""
        print("تحميل بيانات تجريبية...")

        # الحفاظ على المصروفات الحقيقية إذا كانت موجودة
        if not hasattr(self, 'total_expenses') or self.total_expenses == 0:
            self.total_expenses = 95000.0

        # إضافة إيرادات تجريبية
        self.total_revenue = 250000.0
        self.total_pending = 45000.0
        self.net_profit = self.total_revenue - self.total_expenses

        print("تم تحميل البيانات التجريبية")
        print("ملاحظة: هذه بيانات تجريبية لأغراض العرض فقط")

        # عرض رسالة للمستخدم
        try:
            QMessageBox.information(self, "بيانات تجريبية",
                "تم عرض بيانات تجريبية لأن قاعدة البيانات لا تحتوي على إيرادات مسجلة.\n\n"
                "لعرض البيانات الحقيقية، يرجى:\n"
                "1. إضافة مشاريع جديدة\n"
                "2. تسجيل دفعات للمشاريع\n"
                "3. تحديث التقرير")
        except:
            pass

    def get_db_connection(self, year):
        """الحصول على اتصال قاعدة البيانات"""
        try:
            import mysql.connector

            from متغيرات import host, user, password

            db_name = "project_manager_V2"
            print(f"محاولة الاتصال بقاعدة البيانات: {db_name}")

            conn = mysql.connector.connect(
                host=host,
                user=user,
                password=password,
                database=db_name
            )
            print("تم الاتصال بقاعدة البيانات بنجاح")
            return conn
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            # محاولة عرض رسالة للمستخدم
            try:
                QMessageBox.warning(self, "تحذير",
                    f"لا يمكن الاتصال بقاعدة البيانات.\nسيتم عرض بيانات تجريبية.\n\nالخطأ: {e}")
            except:
                pass
            return None

    def update_summary_cards(self):
        """تحديث بطاقات الملخص"""
        try:
            # تحديث بطاقة الإيرادات
            revenue_label = self.revenue_card.findChild(QLabel, "StatValue")
            if revenue_label:
                revenue_label.setText(f"{self.total_revenue:,.0f} د.ل")

            # تحديث بطاقة المصروفات
            expenses_label = self.expenses_card.findChild(QLabel, "StatValue")
            if expenses_label:
                expenses_label.setText(f"{self.total_expenses:,.0f} د.ل")

            # تحديث بطاقة صافي الربح
            profit_label = self.profit_card.findChild(QLabel, "StatValue")
            if profit_label:
                profit_label.setText(f"{self.net_profit:,.0f} د.ل")

            # تحديث بطاقة المبالغ المستحقة
            pending_label = self.pending_card.findChild(QLabel, "StatValue")
            if pending_label:
                pending_label.setText(f"{self.total_pending:,.0f} د.ل")

        except Exception as e:
            print(f"خطأ في تحديث بطاقات الملخص: {e}")

    def load_summary_table(self):
        """تحميل جدول الملخص"""
        try:
            data = [
                ["إجمالي الإيرادات", f"{self.total_revenue:,.0f} د.ل", "100%", "من دفعات المشاريع"],
                ["إجمالي المصروفات", f"{self.total_expenses:,.0f} د.ل",
                 f"{(self.total_expenses/self.total_revenue*100) if self.total_revenue > 0 else 0:.1f}%", "جميع المصروفات"],
                ["صافي الربح", f"{self.net_profit:,.0f} د.ل",
                 f"{(self.net_profit/self.total_revenue*100) if self.total_revenue > 0 else 0:.1f}%", "الإيرادات - المصروفات"],
                ["المبالغ المستحقة", f"{self.total_pending:,.0f} د.ل", "-", "المشاريع غير المكتملة"],
            ]

            self.summary_table.setRowCount(len(data))

            for row, row_data in enumerate(data):
                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الخلايا حسب النوع
                    if col == 1:  # عمود القيمة
                        if "ربح" in row_data[0] and self.net_profit > 0:
                            item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                        elif "ربح" in row_data[0] and self.net_profit < 0:
                            item.setForeground(QColor("#ffeaa7"))  # أحمر فاتح
                        elif "مصروفات" in row_data[0]:
                            item.setForeground(QColor("#fab1a0"))  # أحمر فاتح
                        elif "إيرادات" in row_data[0]:
                            item.setForeground(QColor("#a8e6cf"))  # أخضر فاتح

                    self.summary_table.setItem(row, col, item)

            self.summary_table.resizeRowsToContents()

        except Exception as e:
            print(f"خطأ في تحميل جدول الملخص: {e}")

    def update_chart(self):
        """تحديث الرسم البياني"""
        try:
            if not hasattr(self, 'figure'):
                return

            # التأكد من وجود البيانات المالية
            if not hasattr(self, 'total_revenue'):
                self.total_revenue = 0
                self.total_expenses = 0
                self.net_profit = 0
                self.total_pending = 0

            self.figure.clear()

            chart_type = self.chart_type_combo.currentText()

            if chart_type == "أعمدة بيانية":
                self.create_bar_chart()
            elif chart_type == "رسم دائري":
                self.create_pie_chart()
            elif chart_type == "رسم خطي":
                self.create_line_chart()
            elif chart_type == "رسم مساحي":
                self.create_area_chart()

            self.canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث الرسم البياني: {e}")
            # إنشاء رسم بياني فارغ في حالة الخطأ
            try:
                self.figure.clear()
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض',
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=16)
                ax.set_xticks([])
                ax.set_yticks([])
                self.canvas.draw()
            except:
                pass

    def create_bar_chart(self):
        """إنشاء رسم بياني بالأعمدة"""
        ax = self.figure.add_subplot(111)

        categories = ['الإيرادات', 'المصروفات', 'صافي الربح', 'المستحقات']
        values = [float(self.total_revenue), float(self.total_expenses), float(self.net_profit), float(self.total_pending)]
        colors = ['#27ae60', '#e74c3c', '#3498db', '#f39c12']

        bars = ax.bar(categories, values, color=colors, alpha=0.8)

        # إضافة القيم على الأعمدة
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                   f'{value:,.0f}', ha='center', va='bottom', fontsize=10,
                   fontfamily='Tahoma')

        ax.set_title('الملخص المالي', fontsize=16, fontweight='bold', pad=20,
                    fontfamily='Tahoma')
        ax.set_ylabel('المبلغ (د.ل)', fontsize=12, fontfamily='Tahoma')
        ax.grid(True, alpha=0.3)

        # تنسيق المحاور
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:,.0f}'))

        # تنسيق تسميات المحاور
        for label in ax.get_xticklabels():
            label.set_fontfamily('Tahoma')
            label.set_fontsize(10)

        for label in ax.get_yticklabels():
            label.set_fontfamily('Tahoma')
            label.set_fontsize(9)

        self.figure.tight_layout()

    def create_pie_chart(self):
        """إنشاء رسم دائري"""
        ax = self.figure.add_subplot(111)

        # بيانات الرسم الدائري (الإيرادات والمصروفات فقط)
        if float(self.total_revenue) > 0:
            labels = ['صافي الربح', 'المصروفات']
            sizes = [max(0, float(self.net_profit)), float(self.total_expenses)]
            colors = ['#27ae60', '#e74c3c']
            explode = (0.1, 0)  # تفجير قطعة صافي الربح

            _, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                            explode=explode, autopct='%1.1f%%',
                                            shadow=True, startangle=90)

            # تحسين النصوص
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontfamily('Tahoma')

            # تحسين التسميات
            for text in texts:
                text.set_fontfamily('Tahoma')
                text.set_fontsize(12)

        ax.set_title('توزيع الإيرادات', fontsize=16, fontweight='bold',
                    fontfamily='Tahoma')

    def create_line_chart(self):
        """إنشاء رسم خطي (بيانات شهرية)"""
        ax = self.figure.add_subplot(111)

        # بيانات وهمية للأشهر (يمكن تطويرها لاحقاً)
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']

        # توزيع الإيرادات والمصروفات على الأشهر (بيانات تقديرية)
        monthly_revenue = [float(self.total_revenue)/12 * (1 + 0.2*np.sin(i)) for i in range(12)]
        monthly_expenses = [float(self.total_expenses)/12 * (1 + 0.15*np.cos(i)) for i in range(12)]

        ax.plot(months, monthly_revenue, marker='o', linewidth=2,
               label='الإيرادات', color='#27ae60')
        ax.plot(months, monthly_expenses, marker='s', linewidth=2,
               label='المصروفات', color='#e74c3c')

        ax.set_title('التدفق النقدي الشهري', fontsize=16, fontweight='bold',
                    fontfamily='Tahoma')
        ax.set_ylabel('المبلغ (د.ل)', fontsize=12, fontfamily='Tahoma')

        # تحسين الأسطورة
        legend = ax.legend()
        for text in legend.get_texts():
            text.set_fontfamily('Tahoma')
            text.set_fontsize(10)

        ax.grid(True, alpha=0.3)

        # تنسيق تسميات المحاور
        for label in ax.get_xticklabels():
            label.set_fontfamily('Tahoma')
            label.set_fontsize(9)
            label.set_rotation(45)

        for label in ax.get_yticklabels():
            label.set_fontfamily('Tahoma')
            label.set_fontsize(9)

        self.figure.tight_layout()

    def create_area_chart(self):
        """إنشاء رسم مساحي"""
        ax = self.figure.add_subplot(111)

        months = list(range(1, 13))

        # بيانات تراكمية
        cumulative_revenue = [float(self.total_revenue) * i / 12 for i in months]
        cumulative_expenses = [float(self.total_expenses) * i / 12 for i in months]

        ax.fill_between(months, cumulative_revenue, alpha=0.6,
                       color='#27ae60', label='الإيرادات التراكمية')
        ax.fill_between(months, cumulative_expenses, alpha=0.6,
                       color='#e74c3c', label='المصروفات التراكمية')

        ax.set_title('التدفق النقدي التراكمي', fontsize=16, fontweight='bold',
                    fontfamily='Tahoma')
        ax.set_xlabel('الشهر', fontsize=12, fontfamily='Tahoma')
        ax.set_ylabel('المبلغ التراكمي (د.ل)', fontsize=12, fontfamily='Tahoma')

        # تحسين الأسطورة
        legend = ax.legend()
        for text in legend.get_texts():
            text.set_fontfamily('Tahoma')
            text.set_fontsize(10)

        ax.grid(True, alpha=0.3)

        # تنسيق تسميات المحاور
        for label in ax.get_xticklabels():
            label.set_fontfamily('Tahoma')
            label.set_fontsize(9)

        for label in ax.get_yticklabels():
            label.set_fontfamily('Tahoma')
            label.set_fontsize(9)

        self.figure.tight_layout()

    # وظائف الأحداث
    def on_year_changed(self):
        """عند تغيير السنة"""
        self.load_initial_data()

    def on_period_changed(self):
        """عند تغيير الفترة الزمنية"""
        if self.period_combo.currentText() == "فترة مخصصة":
            self.custom_dates_widget.setVisible(True)
        else:
            self.custom_dates_widget.setVisible(False)

        self.load_initial_data()

    def on_report_type_changed(self):
        """عند تغيير نوع التقرير"""
        self.load_initial_data()

    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.load_initial_data()

    def load_detailed_report(self):
        """تحميل التقرير التفصيلي"""
        report_type = self.detailed_type_combo.currentText()

        # تحديث شريط الحالة
        self.update_status(f"جاري تحميل {report_type}...")

        if report_type == "تقرير المشاريع المالي":
            self.load_projects_financial_report()
        elif report_type == "تقرير العملاء المالي":
            self.load_clients_financial_report()
        elif report_type == "تقرير الموظفين المالي":
            self.load_employees_financial_report()
        elif report_type == "تقرير التدفق النقدي":
            self.load_cash_flow_report()
        elif report_type == "تقرير الأرباح والخسائر":
            self.load_profit_loss_report()
        elif report_type == "تقرير الميزانية العمومية":
            self.load_balance_sheet_report()
        elif report_type == "تقرير تحليل الأداء":
            self.load_performance_analysis_report()
        elif report_type == "تقرير المصروفات التفصيلي":
            self.load_detailed_expenses_report()
        elif report_type == "تقرير العقارات المالي":
            self.load_properties_financial_report()
        elif report_type == "تقرير التدريب المالي":
            self.load_training_financial_report()

        # تحديث الإحصائيات
        self.update_detailed_stats(report_type)
        self.update_status("تم تحميل التقرير بنجاح")

    def load_projects_financial_report(self):
        """تحميل تقرير المشاريع المالي"""
        try:
            headers = ["اسم المشروع", "العميل", "المبلغ الإجمالي", "المدفوع", "الباقي", "الحالة"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT p.اسم_المشروع, c.اسم_العميل, p.المبلغ, p.المدفوع, p.الباقي, p.الحالة
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                ORDER BY p.المبلغ DESC
            """)

            results = cursor.fetchall()
            self.detailed_table.setRowCount(len(results))

            for row, data in enumerate(results):
                for col, key in enumerate(["اسم_المشروع", "اسم_العميل", "المبلغ", "المدفوع", "الباقي", "الحالة"]):
                    value = data.get(key, "")
                    if key in ["المبلغ", "المدفوع", "الباقي"] and value:
                        value = f"{float(value):,.0f} د.ل"

                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير المشاريع: {e}")

    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)

        if hasattr(self, 'last_update_label'):
            self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}")

    # وظائف الأدوات
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_initial_data()

    def export_report(self):
        """تصدير التقرير"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"تقرير_مالي_{self.year_combo.currentText()}.pdf",
                "PDF Files (*.pdf);;Excel Files (*.xlsx)"
            )

            if file_path:
                if file_path.endswith('.pdf'):
                    self.export_to_pdf(file_path)
                elif file_path.endswith('.xlsx'):
                    self.export_to_excel(file_path)

                QMessageBox.information(self, "نجح التصدير", f"تم حفظ التقرير في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء محتوى HTML للطباعة
            html_content = self.generate_print_html()

            # إنشاء ملف مؤقت
            temp_file = os.path.join(os.path.expanduser("~"), "Documents", "temp_financial_report.html")
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح نافذة الطباعة
            from الطباعة import create_window
            create_window(self, temp_file)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def open_settings(self):
        """فتح إعدادات التقارير"""
        QMessageBox.information(self, "الإعدادات", "إعدادات التقارير المالية\n(قيد التطوير)")

    def load_clients_financial_report(self):
        """تحميل تقرير العملاء المالي"""
        try:
            headers = ["اسم العميل", "عدد المشاريع", "إجمالي المبالغ", "إجمالي المدفوع", "إجمالي الباقي"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT
                    c.اسم_العميل,
                    COUNT(p.id) as عدد_المشاريع,
                    SUM(p.المبلغ) as إجمالي_المبالغ,
                    SUM(p.المدفوع) as إجمالي_المدفوع,
                    SUM(p.الباقي) as إجمالي_الباقي
                FROM العملاء c
                LEFT JOIN المشاريع p ON c.id = p.معرف_العميل
                GROUP BY c.id, c.اسم_العميل
                ORDER BY إجمالي_المبالغ DESC
            """)

            results = cursor.fetchall()
            self.detailed_table.setRowCount(len(results))

            for row, data in enumerate(results):
                for col, key in enumerate(["اسم_العميل", "عدد_المشاريع", "إجمالي_المبالغ", "إجمالي_المدفوع", "إجمالي_الباقي"]):
                    value = data.get(key, "")
                    if key in ["إجمالي_المبالغ", "إجمالي_المدفوع", "إجمالي_الباقي"] and value:
                        value = f"{float(value):,.0f} د.ل"

                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير العملاء: {e}")

    def load_employees_financial_report(self):
        """تحميل تقرير الموظفين المالي"""
        try:
            headers = ["اسم الموظف", "الوظيفة", "المرتب", "الرصيد", "السحب", "الصافي"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT اسم_الموظف, الوظيفة, المرتب, الرصيد, السحب,
                       (الرصيد - السحب) as الصافي
                FROM الموظفين
                ORDER BY المرتب DESC
            """)

            results = cursor.fetchall()
            self.detailed_table.setRowCount(len(results))

            for row, data in enumerate(results):
                for col, key in enumerate(["اسم_الموظف", "الوظيفة", "المرتب", "الرصيد", "السحب", "الصافي"]):
                    value = data.get(key, "")
                    if key in ["المرتب", "الرصيد", "السحب", "الصافي"] and value:
                        value = f"{float(value):,.0f} د.ل"

                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير الموظفين: {e}")

    def load_cash_flow_report(self):
        """تحميل تقرير التدفق النقدي"""
        try:
            headers = ["الشهر", "الإيرادات", "المصروفات", "صافي التدفق", "التدفق التراكمي"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # جلب البيانات الشهرية
            monthly_data = []
            cumulative_flow = 0

            for month in range(1, 13):
                # الإيرادات الشهرية
                cursor.execute("""
                    SELECT SUM(المبلغ_المدفوع) as monthly_revenue
                    FROM المشاريع_المدفوعات
                    WHERE MONTH(تاريخ_الدفع) = %s AND YEAR(تاريخ_الدفع) = %s
                """, (month, self.year_combo.currentText()))
                revenue_result = cursor.fetchone()
                monthly_revenue = float(revenue_result['monthly_revenue'] or 0)

                # المصروفات الشهرية
                cursor.execute("""
                    SELECT SUM(المبلغ) as monthly_expenses
                    FROM الحسابات
                    WHERE MONTH(تاريخ_المصروف) = %s AND YEAR(تاريخ_المصروف) = %s
                """, (month, self.year_combo.currentText()))
                expenses_result = cursor.fetchone()
                monthly_expenses = float(expenses_result['monthly_expenses'] or 0)

                # حساب صافي التدفق والتدفق التراكمي
                net_flow = monthly_revenue - monthly_expenses
                cumulative_flow += net_flow

                month_names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                              "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

                monthly_data.append([
                    month_names[month-1],
                    f"{monthly_revenue:,.0f} د.ل",
                    f"{monthly_expenses:,.0f} د.ل",
                    f"{net_flow:,.0f} د.ل",
                    f"{cumulative_flow:,.0f} د.ل"
                ])

            self.detailed_table.setRowCount(len(monthly_data))

            for row, row_data in enumerate(monthly_data):
                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الخلايا حسب القيم
                    if col == 3:  # عمود صافي التدفق
                        if "د.ل" in str(value):
                            amount = float(str(value).replace("د.ل", "").replace(",", "").strip())
                            if amount > 0:
                                item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                            elif amount < 0:
                                item.setForeground(QColor("#ffeaa7"))  # أحمر فاتح

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير التدفق النقدي: {e}")

    def load_profit_loss_report(self):
        """تحميل تقرير الأرباح والخسائر"""
        try:
            headers = ["البيان", "المبلغ", "النسبة من الإيرادات", "الملاحظات"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # حساب الإيرادات
            cursor.execute("""
                SELECT SUM(المبلغ_المدفوع) as total_revenue
                FROM المشاريع_المدفوعات
                WHERE YEAR(تاريخ_الدفع) = %s
            """, (self.year_combo.currentText(),))
            revenue_result = cursor.fetchone()
            total_revenue = float(revenue_result['total_revenue'] or 0)

            # حساب المصروفات بالتصنيف
            cursor.execute("""
                SELECT التصنيف, SUM(المبلغ) as total_amount
                FROM الحسابات
                WHERE YEAR(تاريخ_المصروف) = %s
                GROUP BY التصنيف
                ORDER BY total_amount DESC
            """, (self.year_combo.currentText(),))
            expenses_by_category = cursor.fetchall()

            # إعداد البيانات
            report_data = []

            # إضافة الإيرادات
            report_data.append([
                "إجمالي الإيرادات",
                f"{total_revenue:,.0f} د.ل",
                "100.0%",
                "إجمالي المبالغ المحصلة من المشاريع"
            ])

            # إضافة المصروفات بالتصنيف
            total_expenses = 0
            for expense in expenses_by_category:
                amount = float(expense['total_amount'] or 0)
                total_expenses += amount
                percentage = (amount / total_revenue * 100) if total_revenue > 0 else 0

                report_data.append([
                    f"مصروفات {expense['التصنيف']}",
                    f"{amount:,.0f} د.ل",
                    f"{percentage:.1f}%",
                    f"إجمالي مصروفات {expense['التصنيف']}"
                ])

            # إضافة إجمالي المصروفات
            expenses_percentage = (total_expenses / total_revenue * 100) if total_revenue > 0 else 0
            report_data.append([
                "إجمالي المصروفات",
                f"{total_expenses:,.0f} د.ل",
                f"{expenses_percentage:.1f}%",
                "إجمالي جميع المصروفات"
            ])

            # حساب صافي الربح
            net_profit = total_revenue - total_expenses
            profit_percentage = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
            report_data.append([
                "صافي الربح",
                f"{net_profit:,.0f} د.ل",
                f"{profit_percentage:.1f}%",
                "الإيرادات - المصروفات"
            ])

            # تعبئة الجدول
            self.detailed_table.setRowCount(len(report_data))
            for row, row_data in enumerate(report_data):
                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الصفوف
                    if "إجمالي الإيرادات" in str(row_data[0]):
                        item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                    elif "صافي الربح" in str(row_data[0]):
                        if net_profit > 0:
                            item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                        else:
                            item.setForeground(QColor("#ffeaa7"))  # أحمر فاتح
                    elif "مصروفات" in str(row_data[0]):
                        item.setForeground(QColor("#fab1a0"))  # برتقالي فاتح

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير الأرباح والخسائر: {e}")

    def load_balance_sheet_report(self):
        """تحميل تقرير الميزانية العمومية"""
        try:
            headers = ["البيان", "المبلغ", "النوع", "الملاحظات"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            report_data = []

            # الأصول
            # النقدية (المبالغ المحصلة)
            cursor.execute("""
                SELECT SUM(المبلغ_المدفوع) as cash
                FROM المشاريع_المدفوعات
                WHERE YEAR(تاريخ_الدفع) = %s
            """, (self.year_combo.currentText(),))
            cash_result = cursor.fetchone()
            cash = float(cash_result['cash'] or 0)

            # المبالغ المستحقة
            cursor.execute("""
                SELECT SUM(الباقي) as receivables
                FROM المشاريع
                WHEREالحالة!= 'تم التسليم'
            """)
            receivables_result = cursor.fetchone()
            receivables = float(receivables_result['receivables'] or 0)

            # العقارات (إذا وجدت)
            cursor.execute("""
                SELECT SUM(السعر_المطلوب) as properties_value
                FROM العقارات
                WHEREالحالة= 'متاح'
            """)
            properties_result = cursor.fetchone()
            properties_value = float(properties_result['properties_value'] or 0)

            # إضافة الأصول
            report_data.append(["=== الأصول ===", "", "أصول", ""])
            report_data.append(["النقدية", f"{cash:,.0f} د.ل", "أصول متداولة", "المبالغ المحصلة"])
            report_data.append(["المبالغ المستحقة", f"{receivables:,.0f} د.ل", "أصول متداولة", "مستحقات من العملاء"])
            if properties_value > 0:
                report_data.append(["العقارات", f"{properties_value:,.0f} د.ل", "أصول ثابتة", "قيمة العقارات المتاحة"])

            total_assets = cash + receivables + properties_value
            report_data.append(["إجمالي الأصول", f"{total_assets:,.0f} د.ل", "أصول", ""])

            # الخصوم والحقوق
            # المصروفات المستحقة (تقدير)
            cursor.execute("""
                SELECT SUM(المبلغ) as total_expenses
                FROM الحسابات
                WHERE YEAR(تاريخ_المصروف) = %s
            """, (self.year_combo.currentText(),))
            expenses_result = cursor.fetchone()
            total_expenses = float(expenses_result['total_expenses'] or 0)

            # رواتب الموظفين المستحقة
            cursor.execute("""
                SELECT SUM(الرصيد - السحب) as employee_dues
                FROM الموظفين
                WHERE (الرصيد - السحب) > 0
            """)
            employee_dues_result = cursor.fetchone()
            employee_dues = float(employee_dues_result['employee_dues'] or 0)

            report_data.append(["=== الخصوم ===", "", "خصوم", ""])
            report_data.append(["مستحقات الموظفين", f"{employee_dues:,.0f} د.ل", "خصوم متداولة", "رواتب مستحقة"])

            # حقوق الملكية
            equity = total_assets - employee_dues
            report_data.append(["=== حقوق الملكية ===", "", "حقوق", ""])
            report_data.append(["رأس المال", f"{equity:,.0f} د.ل", "حقوق ملكية", "صافي الأصول"])

            # تعبئة الجدول
            self.detailed_table.setRowCount(len(report_data))
            for row, row_data in enumerate(report_data):
                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين العناوين
                    if "===" in str(row_data[0]):
                        item.setForeground(QColor("#74b9ff"))  # أزرق فاتح
                        item.setForeground(QColor("white"))
                    elif "إجمالي" in str(row_data[0]):
                        item.setForeground(QColor("#ddd"))

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير الميزانية العمومية: {e}")

    def load_performance_analysis_report(self):
        """تحميل تقرير تحليل الأداء"""
        try:
            headers = ["المؤشر", "القيمة الحالية", "القيمة المستهدفة", "الأداء", "التقييم"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # حساب المؤشرات
            current_year = self.year_combo.currentText()

            # معدل إنجاز المشاريع
            cursor.execute("SELECT COUNT(*) as total FROM المشاريع")
            total_projects = cursor.fetchone()['total']

            cursor.execute("SELECT COUNT(*) as completed FROM المشاريع WHEREالحالة= 'تم التسليم'")
            completed_projects = cursor.fetchone()['completed']

            completion_rate = (completed_projects / total_projects * 100) if total_projects > 0 else 0

            # معدل التحصيل
            cursor.execute("SELECT SUM(المبلغ) as total_amount FROM المشاريع")
            total_amount_result = cursor.fetchone()
            total_amount = float(total_amount_result['total_amount'] or 0)

            cursor.execute("SELECT SUM(المدفوع) as paid_amount FROM المشاريع")
            paid_amount_result = cursor.fetchone()
            paid_amount = float(paid_amount_result['paid_amount'] or 0)

            collection_rate = (paid_amount / total_amount * 100) if total_amount > 0 else 0

            # متوسط قيمة المشروع
            avg_project_value = total_amount / total_projects if total_projects > 0 else 0

            # عدد العملاء الجدد
            cursor.execute("""
                SELECT COUNT(*) as new_clients
                FROM العملاء
                WHERE YEAR(تاريخ_الإضافة) = %s
            """, (current_year,))
            new_clients = cursor.fetchone()['new_clients']

            # إعداد البيانات
            report_data = [
                ["معدل إنجاز المشاريع", f"{completion_rate:.1f}%", "85%",
                 "ممتاز" if completion_rate >= 85 else "جيد" if completion_rate >= 70 else "يحتاج تحسين",
                 "✅" if completion_rate >= 85 else "⚠️" if completion_rate >= 70 else "❌"],

                ["معدل التحصيل", f"{collection_rate:.1f}%", "90%",
                 "ممتاز" if collection_rate >= 90 else "جيد" if collection_rate >= 75 else "يحتاج تحسين",
                 "✅" if collection_rate >= 90 else "⚠️" if collection_rate >= 75 else "❌"],

                ["متوسط قيمة المشروع", f"{avg_project_value:,.0f} د.ل", "50,000 د.ل",
                 "ممتاز" if avg_project_value >= 50000 else "جيد" if avg_project_value >= 30000 else "يحتاج تحسين",
                 "✅" if avg_project_value >= 50000 else "⚠️" if avg_project_value >= 30000 else "❌"],

                ["عدد العملاء الجدد", str(new_clients), "20",
                 "ممتاز" if new_clients >= 20 else "جيد" if new_clients >= 10 else "يحتاج تحسين",
                 "✅" if new_clients >= 20 else "⚠️" if new_clients >= 10 else "❌"],

                ["إجمالي المشاريع", str(total_projects), "50",
                 "ممتاز" if total_projects >= 50 else "جيد" if total_projects >= 30 else "يحتاج تحسين",
                 "✅" if total_projects >= 50 else "⚠️" if total_projects >= 30 else "❌"]
            ]

            # تعبئة الجدول
            self.detailed_table.setRowCount(len(report_data))
            for row, row_data in enumerate(report_data):
                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب التقييم
                    if col == 4:  # عمود التقييم
                        if "✅" in str(value):
                            item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                        elif "⚠️" in str(value):
                            item.setForeground(QColor("#fff3cd"))  # أصفر فاتح
                        elif "❌" in str(value):
                            item.setForeground(QColor("#f8d7da"))  # أحمر فاتح

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير تحليل الأداء: {e}")

    def load_detailed_expenses_report(self):
        """تحميل تقرير المصروفات التفصيلي"""
        try:
            headers = ["تاريخ المصروف", "البيان", "التصنيف", "المبلغ", "طريقة الدفع", "الملاحظات"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # جلب المصروفات مع الفلترة حسب الفترة المحددة
            period = self.detailed_period_combo.currentText()
            where_clause = self.get_period_where_clause(period, "تاريخ_المصروف")

            query = f"""
                SELECT تاريخ_المصروف, البيان, التصنيف, المبلغ, طريقة_الدفع, الملاحظات
                FROM الحسابات
                {where_clause}
                ORDER BY تاريخ_المصروف DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            self.detailed_table.setRowCount(len(results))

            total_amount = 0
            for row, data in enumerate(results):
                amount = float(data.get('المبلغ', 0))
                total_amount += amount

                row_data = [
                    data.get('تاريخ_المصروف', '').strftime('%Y-%m-%d') if data.get('تاريخ_المصروف') else '',
                    data.get('البيان', ''),
                    data.get('التصنيف', ''),
                    f"{amount:,.0f} د.ل",
                    data.get('طريقة_الدفع', ''),
                    data.get('الملاحظات', '')
                ]

                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب المبلغ
                    if col == 3:  # عمود المبلغ
                        if amount > 10000:
                            item.setForeground(QColor("#ffcccb"))  # أحمر فاتح للمبالغ الكبيرة
                        elif amount > 5000:
                            item.setForeground(QColor("#fff3cd"))  # أصفر فاتح للمبالغ المتوسطة

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير المصروفات التفصيلي: {e}")

    def load_properties_financial_report(self):
        """تحميل تقرير العقارات المالي"""
        try:
            headers = ["اسم العقار", "النوع", "المساحة", "السعر المطلوب", "الحالة", "تاريخ الإضافة"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # فلترة حسب الحالة
            status_filter = self.detailed_status_combo.currentText()
            where_clause = ""
            if status_filter != "الكل":
                status_map = {
                    "نشط": "متاح",
                    "مكتمل": "تم البيع",
                    "معلق": "معلق",
                    "ملغي": "ملغي"
                }
                if status_filter in status_map:
                    where_clause = f"WHERE الحالة = '{status_map[status_filter]}'"

            query = f"""
                SELECT اسم_العقار, النوع, المساحة, السعر_المطلوب, الحالة, تاريخ_الإضافة
                FROM العقارات
                {where_clause}
                ORDER BY السعر_المطلوب DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            self.detailed_table.setRowCount(len(results))

            for row, data in enumerate(results):
                price = float(data.get('السعر_المطلوب', 0))

                row_data = [
                    data.get('اسم_العقار', ''),
                    data.get('النوع', ''),
                    f"{data.get('المساحة', 0)} م²",
                    f"{price:,.0f} د.ل",
                    data.get('الحالة', ''),
                    data.get('تاريخ_الإضافة', '').strftime('%Y-%m-%d') if data.get('تاريخ_الإضافة') else ''
                ]

                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب الحالة
                    if col == 4:  # عمود الحالة
                        status = str(value)
                        if status == "متاح":
                            item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                        elif status == "تم البيع":
                            item.setForeground(QColor("#cce5ff"))  # أزرق فاتح
                        elif status == "معلق":
                            item.setForeground(QColor("#fff3cd"))  # أصفر فاتح

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير العقارات المالي: {e}")

    def load_training_financial_report(self):
        """تحميل تقرير التدريب المالي"""
        try:
            headers = ["اسم الدورة", "المدرب", "عدد المشاركين", "إجمالي المبلغ", "الحالة", "تاريخ البداية"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # فلترة حسب الحالة
            status_filter = self.detailed_status_combo.currentText()
            where_clause = ""
            if status_filter != "الكل":
                status_map = {
                    "نشط": "جارية",
                    "مكتمل": "مكتملة",
                    "معلق": "قيد التسجيل",
                    "ملغي": "ملغية"
                }
                if status_filter in status_map:
                    where_clause = f"WHEREالحالة= '{status_map[status_filter]}'"

            query = f"""
                SELECT اسم_الدورة, المدرب, عدد_المشاركين, إجمالي_المبلغ, الحالة, تاريخ_البداية
                FROM التدريب
                {where_clause}
                ORDER BY إجمالي_المبلغ DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            self.detailed_table.setRowCount(len(results))

            for row, data in enumerate(results):
                amount = float(data.get('إجمالي_المبلغ', 0))

                row_data = [
                    data.get('اسم_الدورة', ''),
                    data.get('المدرب', ''),
                    str(data.get('عدد_المشاركين', 0)),
                    f"{amount:,.0f} د.ل",
                    data.get('الحالة', ''),
                    data.get('تاريخ_البداية', '').strftime('%Y-%m-%d') if data.get('تاريخ_البداية') else ''
                ]

                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب الحالة
                    if col == 4:  # عمود الحالة
                        status = str(value)
                        if status == "جارية":
                            item.setForeground(QColor("#d5f4e6"))  # أخضر فاتح
                        elif status == "مكتملة":
                            item.setForeground(QColor("#cce5ff"))  # أزرق فاتح
                        elif status == "قيد التسجيل":
                            item.setForeground(QColor("#fff3cd"))  # أصفر فاتح

                    self.detailed_table.setItem(row, col, item)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تقرير التدريب المالي: {e}")

    def get_period_where_clause(self, period, date_column):
        """الحصول على شرط WHERE للفترة الزمنية"""
        current_year = self.year_combo.currentText()

        if period == "السنة كاملة":
            return f"WHERE YEAR({date_column}) = {current_year}"
        elif period == "الربع الأول":
            return f"WHERE YEAR({date_column}) = {current_year} AND MONTH({date_column}) BETWEEN 1 AND 3"
        elif period == "الربع الثاني":
            return f"WHERE YEAR({date_column}) = {current_year} AND MONTH({date_column}) BETWEEN 4 AND 6"
        elif period == "الربع الثالث":
            return f"WHERE YEAR({date_column}) = {current_year} AND MONTH({date_column}) BETWEEN 7 AND 9"
        elif period == "الربع الرابع":
            return f"WHERE YEAR({date_column}) = {current_year} AND MONTH({date_column}) BETWEEN 10 AND 12"
        elif period == "الشهر الحالي":
            current_month = datetime.now().month
            return f"WHERE YEAR({date_column}) = {current_year} AND MONTH({date_column}) = {current_month}"
        elif period == "الشهر السابق":
            last_month = datetime.now().month - 1 if datetime.now().month > 1 else 12
            year = current_year if datetime.now().month > 1 else str(int(current_year) - 1)
            return f"WHERE YEAR({date_column}) = {year} AND MONTH({date_column}) = {last_month}"
        elif period == "آخر 3 أشهر":
            return f"WHERE {date_column} >= DATE_SUB(NOW(), INTERVAL 3 MONTH)"
        elif period == "آخر 6 أشهر":
            return f"WHERE {date_column} >= DATE_SUB(NOW(), INTERVAL 6 MONTH)"
        else:
            return f"WHERE YEAR({date_column}) = {current_year}"

    def save_chart(self):
        """حفظ الرسم البياني"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ الرسم البياني", f"رسم_بياني_{self.year_combo.currentText()}.png",
                "PNG Files (*.png);;PDF Files (*.pdf)"
            )

            if file_path:
                self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                QMessageBox.information(self, "نجح الحفظ", f"تم حفظ الرسم البياني في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الرسم البياني:\n{str(e)}")

    def export_detailed_report(self):
        """تصدير التقرير التفصيلي"""
        try:
            # إنشاء نافذة اختيار نوع التصدير
            export_dialog = QDialog(self)
            export_dialog.setWindowTitle("تصدير التقرير التفصيلي")
            export_dialog.setFixedSize(400, 300)
            export_dialog.setLayoutDirection(Qt.RightToLeft)

            layout = QVBoxLayout(export_dialog)

            # عنوان
            title_label = QLabel("اختر نوع التصدير:")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
            layout.addWidget(title_label)

            # خيارات التصدير
            export_options = QGroupBox("نوع الملف:")
            options_layout = QVBoxLayout(export_options)

            self.export_excel_radio = QRadioButton("📊 Excel (.xlsx) - مع تنسيق متقدم")
            self.export_excel_radio.setChecked(True)
            options_layout.addWidget(self.export_excel_radio)

            self.export_csv_radio = QRadioButton("📄 CSV (.csv) - ملف نصي")
            options_layout.addWidget(self.export_csv_radio)

            self.export_pdf_radio = QRadioButton("📑 PDF (.pdf) - ملف احترافي")
            options_layout.addWidget(self.export_pdf_radio)

            self.export_word_radio = QRadioButton("📝 Word (.docx) - مستند نصي")
            options_layout.addWidget(self.export_word_radio)

            layout.addWidget(export_options)

            # خيارات إضافية
            additional_options = QGroupBox("خيارات إضافية:")
            additional_layout = QVBoxLayout(additional_options)

            self.include_stats_checkbox = QCheckBox("تضمين الإحصائيات")
            self.include_stats_checkbox.setChecked(True)
            additional_layout.addWidget(self.include_stats_checkbox)

            self.include_charts_checkbox = QCheckBox("تضمين الرسوم البيانية")
            additional_layout.addWidget(self.include_charts_checkbox)

            self.auto_open_checkbox = QCheckBox("فتح الملف تلقائياً بعد التصدير")
            self.auto_open_checkbox.setChecked(True)
            additional_layout.addWidget(self.auto_open_checkbox)

            layout.addWidget(additional_options)

            # أزرار
            buttons_layout = QHBoxLayout()

            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_export(export_dialog))
            export_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(export_dialog.reject)
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            buttons_layout.addWidget(export_btn)
            buttons_layout.addWidget(cancel_btn)
            layout.addLayout(buttons_layout)

            export_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التصدير:\n{str(e)}")

    def perform_export(self, dialog):
        """تنفيذ عملية التصدير"""
        try:
            # تحديد نوع الملف
            if self.export_excel_radio.isChecked():
                file_extension = "xlsx"
                file_filter = "Excel Files (*.xlsx)"
            elif self.export_csv_radio.isChecked():
                file_extension = "csv"
                file_filter = "CSV Files (*.csv)"
            elif self.export_pdf_radio.isChecked():
                file_extension = "pdf"
                file_filter = "PDF Files (*.pdf)"
            elif self.export_word_radio.isChecked():
                file_extension = "docx"
                file_filter = "Word Files (*.docx)"
            else:
                file_extension = "xlsx"
                file_filter = "Excel Files (*.xlsx)"

            # اختيار مكان الحفظ
            default_name = f"تقرير_تفصيلي_{self.detailed_type_combo.currentText()}_{self.year_combo.currentText()}.{file_extension}"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي", default_name, file_filter
            )

            if file_path:
                # تنفيذ التصدير حسب النوع
                if file_extension == "xlsx":
                    self.export_to_advanced_excel(file_path)
                elif file_extension == "csv":
                    self.export_table_to_csv(self.detailed_table, file_path)
                elif file_extension == "pdf":
                    self.export_to_advanced_pdf(file_path)
                elif file_extension == "docx":
                    self.export_to_word(file_path)

                # فتح الملف تلقائياً إذا كان مطلوباً
                if self.auto_open_checkbox.isChecked():
                    try:
                        import os
                        os.startfile(file_path)
                    except:
                        pass

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح في:\n{file_path}")
                dialog.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def export_to_advanced_excel(self, file_path):
        """تصدير متقدم إلى Excel مع تنسيق احترافي"""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            # إنشاء كتاب العمل
            wb = Workbook()
            ws = wb.active
            ws.title = "التقرير التفصيلي"

            # إعداد الألوان والخطوط
            header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            data_font = Font(name='Arial', size=10)
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إضافة معلومات التقرير
            ws['A1'] = "منظومة المهندس"
            ws['A1'].font = Font(name='Arial', size=16, bold=True)
            ws['A2'] = self.detailed_type_combo.currentText()
            ws['A2'].font = Font(name='Arial', size=14, bold=True)
            ws['A3'] = f"السنة المالية: {self.year_combo.currentText()}"
            ws['A4'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"

            # إضافة الإحصائيات إذا كانت مطلوبة
            start_row = 6
            if self.include_stats_checkbox.isChecked():
                ws[f'A{start_row}'] = "الإحصائيات:"
                ws[f'A{start_row}'].font = Font(name='Arial', size=12, bold=True)
                start_row += 1

                for i in range(4):
                    title = self.detailed_stats_labels[f"stat_{i}"]["title"].text()
                    value = self.detailed_stats_labels[f"stat_{i}"]["value"].text()
                    if title != "--":
                        ws[f'A{start_row}'] = title
                        ws[f'B{start_row}'] = value
                        start_row += 1

                start_row += 2

            # إضافة بيانات الجدول
            ws[f'A{start_row}'] = "بيانات التقرير:"
            ws[f'A{start_row}'].font = Font(name='Arial', size=12, bold=True)
            start_row += 1

            # إضافة العناوين
            for col in range(self.detailed_table.columnCount()):
                header_item = self.detailed_table.horizontalHeaderItem(col)
                header_text = header_item.text() if header_item else f"عمود {col + 1}"
                cell = ws.cell(row=start_row, column=col + 1, value=header_text)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border
                cell.alignment = Alignment(horizontal='center')

            start_row += 1

            # إضافة البيانات
            for row in range(self.detailed_table.rowCount()):
                for col in range(self.detailed_table.columnCount()):
                    item = self.detailed_table.item(row, col)
                    cell_value = item.text() if item else ""
                    cell = ws.cell(row=start_row + row, column=col + 1, value=cell_value)
                    cell.font = data_font
                    cell.border = border
                    cell.alignment = Alignment(horizontal='center')

            # تعديل عرض الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # حفظ الملف
            wb.save(file_path)

        except ImportError:
            # إذا لم تكن openpyxl متوفرة، استخدم pandas فقط
            self.export_table_to_excel(self.detailed_table, file_path)
        except Exception as e:
            raise e

    def export_to_advanced_pdf(self, file_path):
        """تصدير متقدم إلى PDF"""
        try:
            # إنشاء محتوى HTML متقدم
            html_content = self.generate_detailed_report_html(self.detailed_type_combo.currentText())

            # محاولة استخدام weasyprint للتحويل إلى PDF
            try:
                import weasyprint
                weasyprint.HTML(string=html_content).write_pdf(file_path)
            except ImportError:
                # إذا لم تكن weasyprint متوفرة، احفظ كـ HTML
                html_file_path = file_path.replace('.pdf', '.html')
                with open(html_file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                QMessageBox.information(self, "تنبيه",
                    f"تم إنشاء ملف HTML بدلاً من PDF:\n{html_file_path}\n\n"
                    "يمكنك طباعته كـ PDF من المتصفح أو تثبيت مكتبة weasyprint للتصدير المباشر إلى PDF.")

        except Exception as e:
            raise e

    def export_to_word(self, file_path):
        """تصدير إلى Word"""
        try:
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # إنشاء مستند جديد
            doc = Document()

            # إضافة العنوان
            title = doc.add_heading('منظومة المهندس', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            subtitle = doc.add_heading(self.detailed_type_combo.currentText(), level=1)
            subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # إضافة معلومات التقرير
            info_para = doc.add_paragraph()
            info_para.add_run(f"السنة المالية: {self.year_combo.currentText()}\n")
            info_para.add_run(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # إضافة الإحصائيات إذا كانت مطلوبة
            if self.include_stats_checkbox.isChecked():
                doc.add_heading('الإحصائيات', level=2)

                stats_table = doc.add_table(rows=1, cols=2)
                stats_table.style = 'Table Grid'

                hdr_cells = stats_table.rows[0].cells
                hdr_cells[0].text = 'المؤشر'
                hdr_cells[1].text = 'القيمة'

                for i in range(4):
                    title = self.detailed_stats_labels[f"stat_{i}"]["title"].text()
                    value = self.detailed_stats_labels[f"stat_{i}"]["value"].text()
                    if title != "--":
                        row_cells = stats_table.add_row().cells
                        row_cells[0].text = title
                        row_cells[1].text = value

            # إضافة بيانات التقرير
            doc.add_heading('بيانات التقرير', level=2)

            # إنشاء جدول البيانات
            table = doc.add_table(rows=1, cols=self.detailed_table.columnCount())
            table.style = 'Table Grid'

            # إضافة العناوين
            hdr_cells = table.rows[0].cells
            for col in range(self.detailed_table.columnCount()):
                header_item = self.detailed_table.horizontalHeaderItem(col)
                header_text = header_item.text() if header_item else f"عمود {col + 1}"
                hdr_cells[col].text = header_text

            # إضافة البيانات
            for row in range(self.detailed_table.rowCount()):
                row_cells = table.add_row().cells
                for col in range(self.detailed_table.columnCount()):
                    item = self.detailed_table.item(row, col)
                    cell_text = item.text() if item else ""
                    row_cells[col].text = cell_text

            # حفظ المستند
            doc.save(file_path)

        except ImportError:
            QMessageBox.warning(self, "تحذير",
                "مكتبة python-docx غير مثبتة.\n"
                "يرجى تثبيتها لتصدير ملفات Word:\n"
                "pip install python-docx")
            raise ImportError("python-docx library not installed")
        except Exception as e:
            raise e

    def update_detailed_stats(self, report_type):
        """تحديث إحصائيات التقرير التفصيلي"""
        try:
            conn = self.get_db_connection(self.year_combo.currentText())
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            if report_type == "تقرير المشاريع المالي":
                # إحصائيات المشاريع
                cursor.execute("SELECT COUNT(*) as total FROM المشاريع")
                total = cursor.fetchone()['total']

                cursor.execute("SELECT COUNT(*) as completed FROM المشاريع WHEREالحالة= 'تم التسليم'")
                completed = cursor.fetchone()['completed']

                cursor.execute("SELECT SUM(المبلغ) as total_amount FROM المشاريع")
                total_amount = float(cursor.fetchone()['total_amount'] or 0)

                cursor.execute("SELECT SUM(الباقي) as remaining FROM المشاريع WHEREالحالة!= 'تم التسليم'")
                remaining = float(cursor.fetchone()['remaining'] or 0)

                self.detailed_stats_labels["stat_0"]["title"].setText("إجمالي المشاريع")
                self.detailed_stats_labels["stat_0"]["value"].setText(str(total))

                self.detailed_stats_labels["stat_1"]["title"].setText("مشاريع مكتملة")
                self.detailed_stats_labels["stat_1"]["value"].setText(str(completed))

                self.detailed_stats_labels["stat_2"]["title"].setText("إجمالي القيمة")
                self.detailed_stats_labels["stat_2"]["value"].setText(f"{total_amount:,.0f} د.ل")

                self.detailed_stats_labels["stat_3"]["title"].setText("المبالغ المستحقة")
                self.detailed_stats_labels["stat_3"]["value"].setText(f"{remaining:,.0f} د.ل")

            elif report_type == "تقرير العملاء المالي":
                # إحصائيات العملاء
                cursor.execute("SELECT COUNT(*) as total FROM العملاء")
                total_clients = cursor.fetchone()['total']

                cursor.execute("""
                    SELECT COUNT(DISTINCT p.معرف_العميل) as active_clients
                    FROM المشاريع p
                    WHERE p.الحالة IN ('قيد الإنجاز', 'غير خالص')
                """)
                active_clients = cursor.fetchone()['active_clients']

                cursor.execute("""
                    SELECT AVG(project_count) as avg_projects
                    FROM (
                        SELECT COUNT(*) as project_count
                        FROM المشاريع
                        GROUP BY معرف_العميل
                    ) as client_projects
                """)
                avg_projects = float(cursor.fetchone()['avg_projects'] or 0)

                cursor.execute("""
                    SELECT SUM(p.المبلغ) as total_revenue
                    FROM المشاريع p
                """)
                total_revenue = float(cursor.fetchone()['total_revenue'] or 0)

                self.detailed_stats_labels["stat_0"]["title"].setText("إجمالي العملاء")
                self.detailed_stats_labels["stat_0"]["value"].setText(str(total_clients))

                self.detailed_stats_labels["stat_1"]["title"].setText("عملاء نشطون")
                self.detailed_stats_labels["stat_1"]["value"].setText(str(active_clients))

                self.detailed_stats_labels["stat_2"]["title"].setText("متوسط المشاريع")
                self.detailed_stats_labels["stat_2"]["value"].setText(f"{avg_projects:.1f}")

                self.detailed_stats_labels["stat_3"]["title"].setText("إجمالي الإيرادات")
                self.detailed_stats_labels["stat_3"]["value"].setText(f"{total_revenue:,.0f} د.ل")

            elif report_type == "تقرير المصروفات التفصيلي":
                # إحصائيات المصروفات
                period = self.detailed_period_combo.currentText()
                where_clause = self.get_period_where_clause(period, "تاريخ_المصروف")

                cursor.execute(f"SELECT COUNT(*) as total FROM الحسابات {where_clause}")
                total_expenses = cursor.fetchone()['total']

                cursor.execute(f"SELECT SUM(المبلغ) as total_amount FROM الحسابات {where_clause}")
                total_amount = float(cursor.fetchone()['total_amount'] or 0)

                cursor.execute(f"SELECT AVG(المبلغ) as avg_amount FROM الحسابات {where_clause}")
                avg_amount = float(cursor.fetchone()['avg_amount'] or 0)

                cursor.execute(f"""
                    SELECT التصنيف, SUM(المبلغ) as amount
                    FROM الحسابات {where_clause}
                    GROUP BY التصنيف
                    ORDER BY amount DESC
                    LIMIT 1
                """)
                top_category_result = cursor.fetchone()
                top_category = top_category_result['التصنيف'] if top_category_result else "غير محدد"

                self.detailed_stats_labels["stat_0"]["title"].setText("عدد المصروفات")
                self.detailed_stats_labels["stat_0"]["value"].setText(str(total_expenses))

                self.detailed_stats_labels["stat_1"]["title"].setText("إجمالي المبلغ")
                self.detailed_stats_labels["stat_1"]["value"].setText(f"{total_amount:,.0f} د.ل")

                self.detailed_stats_labels["stat_2"]["title"].setText("متوسط المصروف")
                self.detailed_stats_labels["stat_2"]["value"].setText(f"{avg_amount:,.0f} د.ل")

                self.detailed_stats_labels["stat_3"]["title"].setText("أعلى تصنيف")
                self.detailed_stats_labels["stat_3"]["value"].setText(top_category)

            else:
                # إحصائيات عامة للتقارير الأخرى
                for i in range(4):
                    self.detailed_stats_labels[f"stat_{i}"]["title"].setText("--")
                    self.detailed_stats_labels[f"stat_{i}"]["value"].setText("0")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث إحصائيات التقرير: {e}")

    def print_detailed_report(self):
        """طباعة التقرير التفصيلي"""
        try:
            report_type = self.detailed_type_combo.currentText()

            # إنشاء محتوى HTML للطباعة
            html_content = self.generate_detailed_report_html(report_type)

            # إنشاء ملف مؤقت
            temp_file = os.path.join(os.path.expanduser("~"), "Documents", f"temp_detailed_report_{report_type}.html")
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح نافذة الطباعة
            from الطباعة import create_window
            create_window(self, temp_file)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def generate_detailed_report_html(self, report_type):
        """إنشاء محتوى HTML للتقرير التفصيلي"""
        try:
            # معلومات الشركة
            company_name = "منظومة المهندس"
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M')

            html = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>{report_type} - {self.year_combo.currentText()}</title>
                <style>
                    @page {{
                        size: A4;
                        margin: 2cm;
                    }}
                    body {{
                        font-family: 'Arial', 'Tahoma', sans-serif;
                        margin: 0;
                        padding: 20px;
                        line-height: 1.6;
                        color: #333;
                    }}
                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 3px solid #3498db;
                        padding-bottom: 20px;
                    }}
                    .header h1 {{
                        color: #2c3e50;
                        margin: 0;
                        font-size: 28px;
                    }}
                    .header h2 {{
                        color: #3498db;
                        margin: 10px 0;
                        font-size: 20px;
                    }}
                    .header p {{
                        color: #7f8c8d;
                        margin: 5px 0;
                    }}
                    .stats-section {{
                        display: flex;
                        justify-content: space-around;
                        margin: 20px 0;
                        flex-wrap: wrap;
                    }}
                    .stat-card {{
                        border: 2px solid #3498db;
                        border-radius: 8px;
                        padding: 15px;
                        text-align: center;
                        margin: 5px;
                        min-width: 150px;
                        background-color: #f8f9fa;
                    }}
                    .stat-card h3 {{
                        margin: 0 0 10px 0;
                        color: #2c3e50;
                        font-size: 14px;
                    }}
                    .stat-card p {{
                        margin: 0;
                        font-size: 18px;
                        font-weight: bold;
                        color: #3498db;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 12px;
                    }}
                    th, td {{
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }}
                    th {{
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{
                        background-color: #f8f9fa;
                    }}
                    tr:hover {{
                        background-color: #e3f2fd;
                    }}
                    .footer {{
                        margin-top: 30px;
                        text-align: center;
                        font-size: 10px;
                        color: #7f8c8d;
                        border-top: 1px solid #ddd;
                        padding-top: 10px;
                    }}
                    .summary {{
                        background-color: #e8f5e8;
                        padding: 15px;
                        border-radius: 8px;
                        margin: 20px 0;
                    }}
                    @media print {{
                        .no-print {{ display: none; }}
                        body {{ margin: 0; }}
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{company_name}</h1>
                    <h2>{report_type}</h2>
                    <p>السنة المالية: {self.year_combo.currentText()}</p>
                    <p>تاريخ التقرير: {current_date}</p>
                </div>

                <div class="stats-section">
            """

            # إضافة الإحصائيات
            for i in range(4):
                title = self.detailed_stats_labels[f"stat_{i}"]["title"].text()
                value = self.detailed_stats_labels[f"stat_{i}"]["value"].text()
                if title != "--":
                    html += f"""
                    <div class="stat-card">
                        <h3>{title}</h3>
                        <p>{value}</p>
                    </div>
                    """

            html += """
                </div>

                <table>
                    <thead>
                        <tr>
            """

            # إضافة عناوين الأعمدة
            for col in range(self.detailed_table.columnCount()):
                header_item = self.detailed_table.horizontalHeaderItem(col)
                header_text = header_item.text() if header_item else f"عمود {col + 1}"
                html += f"<th>{header_text}</th>"

            html += """
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة بيانات الجدول
            for row in range(self.detailed_table.rowCount()):
                html += "<tr>"
                for col in range(self.detailed_table.columnCount()):
                    item = self.detailed_table.item(row, col)
                    cell_text = item.text() if item else ""
                    html += f"<td>{cell_text}</td>"
                html += "</tr>"

            html += f"""
                    </tbody>
                </table>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير بواسطة {company_name} - جميع الحقوق محفوظة</p>
                    <p>تاريخ الطباعة: {current_date}</p>
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            print(f"خطأ في إنشاء HTML للتقرير التفصيلي: {e}")
            return "<html><body><h1>خطأ في إنشاء التقرير</h1></body></html>"

    def export_table_to_excel(self, table, file_path):
        """تصدير الجدول إلى Excel"""
        try:
            import pandas as pd

            # جمع البيانات من الجدول
            data = []
            headers = []

            # جلب العناوين
            for col in range(table.columnCount()):
                header_item = table.horizontalHeaderItem(col)
                headers.append(header_item.text() if header_item else f"Column {col}")

            # جلب البيانات
            for row in range(table.rowCount()):
                row_data = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(data, columns=headers)
            df.to_excel(file_path, index=False, engine='openpyxl')

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة pandas غير مثبتة. يرجى تثبيتها لتصدير Excel.")
        except Exception as e:
            raise e

    def export_table_to_csv(self, table, file_path):
        """تصدير الجدول إلى CSV"""
        try:
            import csv

            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = []
                for col in range(table.columnCount()):
                    header_item = table.horizontalHeaderItem(col)
                    headers.append(header_item.text() if header_item else f"Column {col}")
                writer.writerow(headers)

                # كتابة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

        except Exception as e:
            raise e

    def export_to_pdf(self, file_path):
        """تصدير التقرير إلى PDF"""
        try:
            html_content = self.generate_print_html()

            # استخدام مكتبة weasyprint أو reportlab لتحويل HTML إلى PDF
            # هذا مثال بسيط - يمكن تحسينه
            with open(file_path.replace('.pdf', '.html'), 'w', encoding='utf-8') as f:
                f.write(html_content)

            QMessageBox.information(self, "تنبيه", "تم إنشاء ملف HTML. يمكنك طباعته كـ PDF من المتصفح.")

        except Exception as e:
            raise e

    def export_to_excel(self, file_path):
        """تصدير التقرير إلى Excel"""
        try:
            self.export_table_to_excel(self.summary_table, file_path)
        except Exception as e:
            raise e

    def generate_print_html(self):
        """إنشاء محتوى HTML للطباعة"""
        try:
            html = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>التقرير المالي - {self.year_combo.currentText()}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ text-align: center; margin-bottom: 30px; }}
                    .stats {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                    .stat-card {{ border: 2px solid #ddd; padding: 15px; border-radius: 8px; text-align: center; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #f2f2f2; }}
                    .revenue {{ color: #27ae60; }}
                    .expense {{ color: #e74c3c; }}
                    .profit {{ color: #3498db; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>التقرير المالي لعام {self.year_combo.currentText()}</h1>
                    <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}</p>
                </div>

                <div class="stats">
                    <div class="stat-card revenue">
                        <h3>إجمالي الإيرادات</h3>
                        <p>{self.total_revenue:,.0f} د.ل</p>
                    </div>
                    <div class="stat-card expense">
                        <h3>إجمالي المصروفات</h3>
                        <p>{self.total_expenses:,.0f} د.ل</p>
                    </div>
                    <div class="stat-card profit">
                        <h3>صافي الربح</h3>
                        <p>{self.net_profit:,.0f} د.ل</p>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>القيمة</th>
                            <th>النسبة المئوية</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة بيانات الجدول
            for row in range(self.summary_table.rowCount()):
                html += "<tr>"
                for col in range(self.summary_table.columnCount()):
                    item = self.summary_table.item(row, col)
                    html += f"<td>{item.text() if item else ''}</td>"
                html += "</tr>"

            html += """
                    </tbody>
                </table>
            </body>
            </html>
            """

            return html

        except Exception as e:
            print(f"خطأ في إنشاء HTML: {e}")
            return "<html><body><h1>خطأ في إنشاء التقرير</h1></body></html>"

    def perform_comparison(self):
        """تنفيذ المقارنة بين السنوات"""
        try:
            year1 = self.year1_combo.currentText()
            year2 = self.year2_combo.currentText()

            if year1 == year2:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار سنتين مختلفتين للمقارنة.")
                return

            # مسح النتائج السابقة
            for i in reversed(range(self.comparison_layout.count())):
                self.comparison_layout.itemAt(i).widget().setParent(None)

            # جلب البيانات للسنتين
            data1 = self.get_year_financial_data(year1)
            data2 = self.get_year_financial_data(year2)

            # إنشاء جدول المقارنة
            comparison_table = QTableWidget()
            comparison_table.setColumnCount(3)
            comparison_table.setHorizontalHeaderLabels(["البيان", f"سنة {year1}", f"سنة {year2}"])

            comparison_data = [
                ["إجمالي الإيرادات", f"{data1['revenue']:,.0f} د.ل", f"{data2['revenue']:,.0f} د.ل"],
                ["إجمالي المصروفات", f"{data1['expenses']:,.0f} د.ل", f"{data2['expenses']:,.0f} د.ل"],
                ["صافي الربح", f"{data1['profit']:,.0f} د.ل", f"{data2['profit']:,.0f} د.ل"],
                ["المبالغ المستحقة", f"{data1['pending']:,.0f} د.ل", f"{data2['pending']:,.0f} د.ل"],
            ]

            comparison_table.setRowCount(len(comparison_data))

            for row, row_data in enumerate(comparison_data):
                for col, value in enumerate(row_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    comparison_table.setItem(row, col, item)

            comparison_table.resizeColumnsToContents()
            self.comparison_layout.addWidget(comparison_table)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تنفيذ المقارنة:\n{str(e)}")

    def get_year_financial_data(self, year):
        """جلب البيانات المالية لسنة محددة"""
        try:
            conn = self.get_db_connection(year)
            if not conn:
                return {"revenue": 0, "expenses": 0, "profit": 0, "pending": 0}

            cursor = conn.cursor(dictionary=True)

            # الإيرادات
            cursor.execute("SELECT SUM(المبلغ_المدفوع) as total FROM المشاريع_المدفوعات WHERE YEAR(تاريخ_الدفع) = %s", (year,))
            revenue = float(cursor.fetchone()['total'] or 0)

            # المصروفات
            cursor.execute("SELECT SUM(المبلغ) as total FROM الحسابات WHERE YEAR(تاريخ_المصروف) = %s", (year,))
            expenses = float(cursor.fetchone()['total'] or 0)

            # المبالغ المستحقة
            cursor.execute("SELECT SUM(الباقي) as total FROM المشاريع WHEREالحالة!= 'تم التسليم'")
            pending = float(cursor.fetchone()['total'] or 0)

            cursor.close()
            conn.close()

            return {
                "revenue": revenue,
                "expenses": expenses,
                "profit": revenue - expenses,
                "pending": pending
            }

        except Exception as e:
            print(f"خطأ في جلب البيانات المالية للسنة {year}: {e}")
            return {"revenue": 0, "expenses": 0, "profit": 0, "pending": 0}


# دالة لفتح نافذة التقارير المالية
def open_financial_reports_window(parent=None):
    """فتح نافذة التقارير المالية"""
    try:
        window = FinancialReportsWindow(parent)
        window.show()
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة التقارير المالية: {e}")
        if parent:
            QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة التقارير المالية:\n{str(e)}")
        return None
