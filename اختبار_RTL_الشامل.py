#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لدعم RTL في النظام المحاسبي
يختبر جميع مكونات النظام للتأكد من دعم RTL الصحيح
"""

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QComboBox, QDateEdit, QLineEdit, QTextEdit, QProgressBar, QMessageBox,
    QSplitter, QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox,
    QCheckBox, QFileDialog, QTreeWidget, QTreeWidgetItem,
    QScrollArea, QGridLayout, QMenu, QTabWidget, QWidget
)
from PySide6.QtCore import Qt, QDate, QThread, QTimer, Signal
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen, QIcon

# استيراد الوحدات المطلوبة
try:
    from الإعدادات_العامة import *
    from ستايل import *
    from النظام_المحاسبي_الموحد import UnifiedAccountingSystem
    from الأدوات import AddEntryDialog
    from مراحل_المشروع import ProjectPhasesWindow
    from حوارات_العهد import CustodyDialog, ExpenseDialog
    print("✅ تم تحميل جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل الوحدات: {e}")

class RTLTestWindow(QMainWindow):
    """نافذة اختبار شاملة لدعم RTL"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.create_ui()
        self.run_rtl_tests()
        
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.setWindowTitle("اختبار دعم RTL الشامل - النظام المحاسبي")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق RTL شامل
        try:
            apply_comprehensive_rtl(self)
            print("✅ تم تطبيق RTL شامل على النافذة الرئيسية")
        except Exception as e:
            print(f"⚠️ تحذير: {e}")
            self.setLayoutDirection(Qt.RightToLeft)
        
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setLayoutDirection(Qt.RightToLeft)
        
        # العنوان الرئيسي
        title_label = QLabel("اختبار دعم RTL الشامل")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; padding: 20px;")
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        try:
            apply_rtl_to_tab_widget(self.tab_widget)
            print("✅ تم تطبيق RTL على التبويبات")
        except:
            self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        
        # تبويب اختبار الجداول
        self.create_table_test_tab()
        
        # تبويب اختبار النوافذ
        self.create_dialog_test_tab()
        
        # تبويب اختبار القوائم
        self.create_menu_test_tab()
        
        # تبويب اختبار النماذج
        self.create_form_test_tab()
        
        # تبويب النتائج
        self.create_results_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز للاختبار")
        self.status_label.setAlignment(Qt.AlignRight)
        main_layout.addWidget(self.status_label)
        
    def create_table_test_tab(self):
        """إنشاء تبويب اختبار الجداول"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # عنوان القسم
        section_label = QLabel("اختبار الجداول وعرض البيانات")
        section_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e;")
        layout.addWidget(section_label)
        
        # جدول اختبار
        self.test_table = QTableWidget(5, 4)
        self.test_table.setHorizontalHeaderLabels(["الاسم", "العمر", "المدينة", "الراتب"])
        
        # إضافة بيانات تجريبية
        test_data = [
            ["أحمد محمد", "30", "الرياض", "5000"],
            ["فاطمة علي", "25", "جدة", "4500"],
            ["محمد سعد", "35", "الدمام", "6000"],
            ["نورا أحمد", "28", "مكة", "4800"],
            ["سعد محمد", "32", "المدينة", "5200"]
        ]
        
        for row, row_data in enumerate(test_data):
            for col, data in enumerate(row_data):
                item = QTableWidgetItem(data)
                self.test_table.setItem(row, col, item)
        
        # تطبيق RTL على الجدول
        try:
            apply_rtl_to_table(self.test_table)
            print("✅ تم تطبيق RTL على جدول الاختبار")
        except Exception as e:
            print(f"⚠️ تحذير في جدول الاختبار: {e}")
            self.test_table.setLayoutDirection(Qt.RightToLeft)
        
        layout.addWidget(self.test_table)
        
        # أزرار اختبار الجدول
        table_buttons_layout = QHBoxLayout()
        table_buttons_layout.setLayoutDirection(Qt.RightToLeft)
        
        test_table_btn = QPushButton("اختبار محاذاة الجدول")
        test_table_btn.clicked.connect(self.test_table_alignment)
        table_buttons_layout.addWidget(test_table_btn)
        
        test_context_menu_btn = QPushButton("اختبار قائمة السياق")
        test_context_menu_btn.clicked.connect(self.test_table_context_menu)
        table_buttons_layout.addWidget(test_context_menu_btn)
        
        layout.addLayout(table_buttons_layout)
        
        self.tab_widget.addTab(tab, "اختبار الجداول")
        
    def create_dialog_test_tab(self):
        """إنشاء تبويب اختبار النوافذ"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        section_label = QLabel("اختبار النوافذ والحوارات")
        section_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e;")
        layout.addWidget(section_label)
        
        # أزرار اختبار النوافذ
        dialog_buttons_layout = QGridLayout()
        dialog_buttons_layout.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار النظام المحاسبي
        accounting_btn = QPushButton("اختبار النظام المحاسبي")
        accounting_btn.clicked.connect(self.test_accounting_system)
        dialog_buttons_layout.addWidget(accounting_btn, 0, 0)
        
        # اختبار حوار الإضافة
        add_dialog_btn = QPushButton("اختبار حوار الإضافة")
        add_dialog_btn.clicked.connect(self.test_add_dialog)
        dialog_buttons_layout.addWidget(add_dialog_btn, 0, 1)
        
        # اختبار حوار المراحل
        phases_btn = QPushButton("اختبار مراحل المشروع")
        phases_btn.clicked.connect(self.test_project_phases)
        dialog_buttons_layout.addWidget(phases_btn, 1, 0)
        
        # اختبار حوار العهدة
        custody_btn = QPushButton("اختبار حوار العهدة")
        custody_btn.clicked.connect(self.test_custody_dialog)
        dialog_buttons_layout.addWidget(custody_btn, 1, 1)
        
        layout.addLayout(dialog_buttons_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "اختبار النوافذ")
        
    def create_menu_test_tab(self):
        """إنشاء تبويب اختبار القوائم"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        section_label = QLabel("اختبار القوائم والعناصر التفاعلية")
        section_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e;")
        layout.addWidget(section_label)
        
        # اختبار القائمة المنسدلة
        combo_label = QLabel("اختبار القائمة المنسدلة:")
        layout.addWidget(combo_label)
        
        self.test_combo = QComboBox()
        self.test_combo.addItems(["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "خيار باللغة العربية"])
        try:
            apply_rtl_to_combo_box(self.test_combo)
            print("✅ تم تطبيق RTL على القائمة المنسدلة")
        except:
            self.test_combo.setLayoutDirection(Qt.RightToLeft)
        layout.addWidget(self.test_combo)
        
        # اختبار زر القائمة
        menu_btn = QPushButton("اختبار قائمة السياق")
        menu_btn.clicked.connect(self.show_test_menu)
        layout.addWidget(menu_btn)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "اختبار القوائم")
        
    def create_form_test_tab(self):
        """إنشاء تبويب اختبار النماذج"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        section_label = QLabel("اختبار النماذج وحقول الإدخال")
        section_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e;")
        layout.addWidget(section_label)
        
        # نموذج اختبار
        form_group = QGroupBox("نموذج اختبار RTL")
        form_layout = QFormLayout(form_group)
        
        try:
            setup_rtl_form_layout(form_layout)
            print("✅ تم تطبيق RTL على تخطيط النموذج")
        except:
            form_layout.setLayoutDirection(Qt.RightToLeft)
        
        # حقول الإدخال
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("أدخل الاسم هنا")
        name_edit.setLayoutDirection(Qt.RightToLeft)
        name_edit.setAlignment(Qt.AlignRight)
        form_layout.addRow("الاسم:", name_edit)
        
        age_spin = QSpinBox()
        age_spin.setLayoutDirection(Qt.RightToLeft)
        form_layout.addRow("العمر:", age_spin)
        
        date_edit = QDateEdit()
        date_edit.setLayoutDirection(Qt.RightToLeft)
        date_edit.setDate(QDate.currentDate())
        form_layout.addRow("التاريخ:", date_edit)
        
        notes_edit = QTextEdit()
        notes_edit.setLayoutDirection(Qt.RightToLeft)
        notes_edit.setPlaceholderText("أدخل الملاحظات هنا...")
        form_layout.addRow("الملاحظات:", notes_edit)
        
        layout.addWidget(form_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "اختبار النماذج")
        
    def create_results_tab(self):
        """إنشاء تبويب النتائج"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        section_label = QLabel("نتائج اختبار RTL")
        section_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e;")
        layout.addWidget(section_label)
        
        self.results_text = QTextEdit()
        self.results_text.setLayoutDirection(Qt.RightToLeft)
        self.results_text.setReadOnly(True)
        layout.addWidget(self.results_text)
        
        # زر تشغيل جميع الاختبارات
        run_all_btn = QPushButton("تشغيل جميع الاختبارات")
        run_all_btn.clicked.connect(self.run_all_tests)
        layout.addWidget(run_all_btn)
        
        self.tab_widget.addTab(tab, "النتائج")

    def run_rtl_tests(self):
        """تشغيل اختبارات RTL الأساسية"""
        self.test_results = []

        # اختبار الاتجاه الأساسي
        if self.layoutDirection() == Qt.RightToLeft:
            self.test_results.append("✅ اتجاه النافذة الرئيسية: RTL")
        else:
            self.test_results.append("❌ اتجاه النافذة الرئيسية: LTR")

        # اختبار التبويبات
        if self.tab_widget.layoutDirection() == Qt.RightToLeft:
            self.test_results.append("✅ اتجاه التبويبات: RTL")
        else:
            self.test_results.append("❌ اتجاه التبويبات: LTR")

        # اختبار الجدول
        if hasattr(self, 'test_table') and self.test_table.layoutDirection() == Qt.RightToLeft:
            self.test_results.append("✅ اتجاه الجدول: RTL")
        else:
            self.test_results.append("❌ اتجاه الجدول: LTR")

        print("تم تشغيل الاختبارات الأساسية")

    def test_table_alignment(self):
        """اختبار محاذاة الجدول"""
        try:
            apply_table_item_rtl_alignment(self.test_table)
            self.status_label.setText("✅ تم اختبار محاذاة الجدول بنجاح")
            self.test_results.append("✅ محاذاة عناصر الجدول: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في اختبار محاذاة الجدول: {e}")
            self.test_results.append(f"❌ محاذاة عناصر الجدول: فشل - {e}")

    def test_table_context_menu(self):
        """اختبار قائمة السياق للجدول"""
        try:
            menu_items = [
                {'text': '👁️ عرض', 'callback': lambda: print("عرض")},
                {'text': '✏️ تعديل', 'callback': lambda: print("تعديل")},
                'separator',
                {'text': '🗑️ حذف', 'callback': lambda: print("حذف")}
            ]
            setup_context_menu_rtl(self.test_table, menu_items)
            self.status_label.setText("✅ تم إعداد قائمة السياق للجدول")
            self.test_results.append("✅ قائمة سياق الجدول: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في قائمة السياق: {e}")
            self.test_results.append(f"❌ قائمة سياق الجدول: فشل - {e}")

    def test_accounting_system(self):
        """اختبار النظام المحاسبي"""
        try:
            accounting_window = UnifiedAccountingSystem(self)
            accounting_window.show()
            self.status_label.setText("✅ تم فتح النظام المحاسبي")
            self.test_results.append("✅ النظام المحاسبي: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في النظام المحاسبي: {e}")
            self.test_results.append(f"❌ النظام المحاسبي: فشل - {e}")

    def test_add_dialog(self):
        """اختبار حوار الإضافة"""
        try:
            dialog = AddEntryDialog(self, "اختبار", "2024")
            dialog.show()
            self.status_label.setText("✅ تم فتح حوار الإضافة")
            self.test_results.append("✅ حوار الإضافة: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في حوار الإضافة: {e}")
            self.test_results.append(f"❌ حوار الإضافة: فشل - {e}")

    def test_project_phases(self):
        """اختبار مراحل المشروع"""
        try:
            # بيانات مشروع تجريبية
            project_data = {
                'معرف_المشروع': 1,
                'اسم_المشروع': 'مشروع اختبار',
                'نوع_المشروع': 'تصميم'
            }
            phases_window = ProjectPhasesWindow(project_data, self)
            phases_window.show()
            self.status_label.setText("✅ تم فتح نافذة مراحل المشروع")
            self.test_results.append("✅ مراحل المشروع: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في مراحل المشروع: {e}")
            self.test_results.append(f"❌ مراحل المشروع: فشل - {e}")

    def test_custody_dialog(self):
        """اختبار حوار العهدة"""
        try:
            custody_dialog = CustodyDialog(self, project_id=1)
            custody_dialog.show()
            self.status_label.setText("✅ تم فتح حوار العهدة")
            self.test_results.append("✅ حوار العهدة: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في حوار العهدة: {e}")
            self.test_results.append(f"❌ حوار العهدة: فشل - {e}")

    def show_test_menu(self):
        """عرض قائمة اختبار"""
        try:
            menu = QMenu(self)
            apply_rtl_to_menu(menu)

            menu.addAction("الخيار الأول")
            menu.addAction("الخيار الثاني")
            menu.addSeparator()
            menu.addAction("خيار فرعي")

            # عرض القائمة
            menu.exec(self.mapToGlobal(self.sender().pos()))
            self.status_label.setText("✅ تم عرض قائمة الاختبار")
            self.test_results.append("✅ قائمة الاختبار: نجح")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في قائمة الاختبار: {e}")
            self.test_results.append(f"❌ قائمة الاختبار: فشل - {e}")

    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        self.test_results.clear()

        # تشغيل الاختبارات
        self.run_rtl_tests()
        self.test_table_alignment()
        self.test_table_context_menu()

        # عرض النتائج
        results_text = "نتائج اختبار RTL الشامل\n"
        results_text += "=" * 50 + "\n\n"

        for result in self.test_results:
            results_text += result + "\n"

        results_text += "\n" + "=" * 50 + "\n"
        results_text += f"تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

        # حساب الإحصائيات
        passed_tests = len([r for r in self.test_results if r.startswith("✅")])
        failed_tests = len([r for r in self.test_results if r.startswith("❌")])
        total_tests = passed_tests + failed_tests

        results_text += f"إجمالي الاختبارات: {total_tests}\n"
        results_text += f"الاختبارات الناجحة: {passed_tests}\n"
        results_text += f"الاختبارات الفاشلة: {failed_tests}\n"

        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
            results_text += f"معدل النجاح: {success_rate:.1f}%\n"

        self.results_text.setText(results_text)
        self.tab_widget.setCurrentIndex(4)  # الانتقال لتبويب النتائج

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)

    # تطبيق RTL على التطبيق بأكمله
    app.setLayoutDirection(Qt.RightToLeft)

    # تطبيق الأنماط
    try:
        app.setStyleSheet(get_rtl_stylesheet())
        print("✅ تم تطبيق أنماط RTL على التطبيق")
    except:
        print("⚠️ تحذير: لم يتم تطبيق أنماط RTL")

    # إنشاء وعرض نافذة الاختبار
    test_window = RTLTestWindow()
    test_window.show()

    print("🚀 تم بدء اختبار RTL الشامل")
    print("استخدم النوافذ والتبويبات لاختبار مختلف مكونات النظام")

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
