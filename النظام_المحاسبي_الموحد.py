#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام المحاسبي الشامل الموحد
يدمج جميع واجهات التقارير المحاسبية والمالية في نظام واحد متكامل
"""

import sys
import os
from datetime import datetime, date, timedelta
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QComboBox, QDateEdit, QLineEdit, QTextEdit, QProgressBar, QMessageBox,
    QSplitter, QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox,
    QCheckBox, QFileDialog, QApplication, QTreeWidget, QTreeWidgetItem,
    QScrollArea, QGridLayout, QMenu
)
from PySide6.QtCore import Qt, QDate, QThread, QTimer, Signal
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen, QIcon
from PySide6.QtPrintSupport import QPrinter, QPrintDialog
import mysql.connector

# استيراد الوحدات المطلوبة
try:
    from الدوال_الأساسية import *
    from قاعدة_البيانات import *
    from ستايل import *
    # استيراد الوحدات المحاسبية مع معالجة الأخطاء
    try:
        from تكامل_المحاسبة import AccountingIntegration
    except ImportError:
        print("تحذير: لم يتم العثور على وحدة accounting_integration")
        AccountingIntegration = None

    try:
        from تنسيق_التقارير_المالية import apply_financial_reports_style
    except ImportError:
        print("تحذير: لم يتم العثور على وحدة financial_reports_style")
        def apply_financial_reports_style(widget):
            pass

except ImportError as e:
    print(f"تعذر استيراد بعض الوحدات الأساسية: {e}")

class UnifiedAccountingSystem(QDialog):
    """النظام المحاسبي الشامل الموحد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent

        # تهيئة الوحدات المحاسبية مع معالجة الأخطاء
        if AccountingIntegration:
            self.accounting = AccountingIntegration()
        else:
            self.accounting = None

        self.setup_window()
        self.create_ui()
        self.load_initial_data()
        self.apply_styles()
        
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.setWindowTitle("النظام المحاسبي الشامل - منظومة المهندس")
        self.setGeometry(50, 50, 1800, 1000)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(False)
        
        # إعداد أيقونة النافذة
        try:
            icons_directory = icons_dir if 'icons_dir' in globals() else 'icons'
            icon_path = os.path.join(icons_directory, 'فلوس.svg')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
            
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء الشريط العلوي
        self.create_header_section(main_layout)
        
        # إنشاء التبويبات الرئيسية
        self.create_main_tabs(main_layout)
        
        # إنشاء شريط الحالة
        self.create_status_bar(main_layout)
        
    def create_header_section(self, parent_layout):
        """إنشاء القسم العلوي"""
        header_frame = QFrame()
        header_frame.setObjectName("HeaderFrame")
        header_frame.setFixedHeight(200)  # زيادة الارتفاع
        header_layout = QVBoxLayout(header_frame)  # تغيير إلى تخطيط عمودي
        header_layout.setContentsMargins(15, 10, 15, 10)
        header_layout.setSpacing(10)

        # الصف الأول - العنوان الرئيسي
        title_row = QHBoxLayout()
        title_label = QLabel("النظام المحاسبي الشامل")
        title_label.setObjectName("MainTitle")
        title_label.setAlignment(Qt.AlignCenter)
        title_row.addWidget(title_label)
        header_layout.addLayout(title_row)

        # الصف الثاني - معلومات سريعة
        info_row = QHBoxLayout()
        self.create_quick_info_panel(info_row)
        header_layout.addLayout(info_row)

        parent_layout.addWidget(header_frame)
        
    def create_quick_info_panel(self, parent_layout):
        """إنشاء لوحة المعلومات السريعة"""
        # إضافة مساحة فارغة في البداية
        parent_layout.addStretch(1)

        # بطاقات المعلومات السريعة مع تباعد مناسب
        self.total_revenue_card = self.create_info_card("إجمالي الإيرادات", "0.00", "#2ecc71")
        parent_layout.addWidget(self.total_revenue_card)

        # إضافة مساحة صغيرة بين البطاقات
        parent_layout.addSpacing(15)

        self.total_expenses_card = self.create_info_card("إجمالي المصروفات", "0.00", "#e74c3c")
        parent_layout.addWidget(self.total_expenses_card)

        parent_layout.addSpacing(15)

        self.net_profit_card = self.create_info_card("صافي الربح", "0.00", "#3498db")
        parent_layout.addWidget(self.net_profit_card)

        parent_layout.addSpacing(15)

        self.pending_amount_card = self.create_info_card("المبالغ المعلقة", "0.00", "#f39c12")
        parent_layout.addWidget(self.pending_amount_card)

        # إضافة مساحة فارغة في النهاية
        parent_layout.addStretch(1)
        
    def create_info_card(self, title, value, color):
        """إنشاء بطاقة معلومات محسنة"""
        card = QFrame()
        card.setObjectName("InfoCard")
        card.setFixedSize(200, 65)  # زيادة الحجم قليلاً
        card.setStyleSheet(f"""
            QFrame#InfoCard {{
                background: white;
                border: 2px solid {color};
                border-radius: 8px;
                margin: 3px;
            }}
            QFrame#InfoCard:hover {{
                background: {color}15;
                border: 2px solid {color};
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(2)

        title_label = QLabel(title)
        title_label.setObjectName("CardTitle")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            color: #6c757d;
            font-size: 11px;
            font-weight: bold;
            margin: 0px;
        """)

        value_label = QLabel(value)
        value_label.setObjectName("CardValue")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            color: {color};
            font-weight: bold;
            font-size: 16px;
            margin: 0px;
        """)

        layout.addWidget(title_label)
        layout.addWidget(value_label)

        # حفظ مرجع للـ value_label لتحديثه لاحقاً
        card.value_label = value_label

        return card

    def update_info_card_value(self, card, new_value):
        """تحديث قيمة بطاقة المعلومات"""
        if hasattr(card, 'value_label'):
            card.value_label.setText(new_value)
        
    def create_main_tabs(self, parent_layout):
        """إنشاء التبويبات الرئيسية"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("MainTabWidget")
        
        # تبويب التقارير المالية العامة
        self.create_financial_reports_tab()
        
        # تبويب التقارير المحاسبية المتقدمة
        self.create_accounting_reports_tab()
        
        # تبويب التقارير التفصيلية
        self.create_detailed_reports_tab()
        
        # تبويب القيود المحاسبية
        self.create_journal_entries_tab()
        
        # تبويب شجرة الحسابات
        self.create_account_tree_tab()
        
        # تبويب التحليلات والرسوم البيانية
        self.create_analytics_tab()
        
        parent_layout.addWidget(self.tab_widget)
        
    def create_financial_reports_tab(self):
        """إنشاء تبويب التقارير المالية العامة"""
        self.financial_tab = QWidget()
        layout = QVBoxLayout(self.financial_tab)
        
        # شريط التحكم
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        
        # اختيار السنة
        control_layout.addWidget(QLabel("السنة:"))
        self.year_combo = QComboBox()
        self.populate_years(self.year_combo)
        control_layout.addWidget(self.year_combo)
        
        # اختيار نوع التقرير
        control_layout.addWidget(QLabel("نوع التقرير:"))
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "ملخص مالي شامل",
            "تقرير المشاريع المالي", 
            "تقرير العملاء المالي",
            "تقرير الموظفين المالي",
            "تقرير التدفق النقدي",
            "مقارنة السنوات"
        ])
        control_layout.addWidget(self.report_type_combo)
        
        # أزرار التحكم
        self.generate_report_btn = QPushButton("إنشاء التقرير")
        self.export_excel_btn = QPushButton("تصدير Excel")
        self.print_report_btn = QPushButton("طباعة")
        
        control_layout.addWidget(self.generate_report_btn)
        control_layout.addWidget(self.export_excel_btn)
        control_layout.addWidget(self.print_report_btn)
        control_layout.addStretch()
        
        layout.addWidget(control_frame)
        
        # جدول التقرير
        self.financial_table = QTableWidget()
        self.financial_table.setAlternatingRowColors(True)
        layout.addWidget(self.financial_table)
        
        # ربط الأحداث
        self.generate_report_btn.clicked.connect(self.generate_financial_report)
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        self.print_report_btn.clicked.connect(self.print_report)
        
        self.tab_widget.addTab(self.financial_tab, "التقارير المالية العامة")
        
    def create_accounting_reports_tab(self):
        """إنشاء تبويب التقارير المحاسبية المتقدمة"""
        self.accounting_tab = QWidget()
        layout = QVBoxLayout(self.accounting_tab)

        # شريط التحكم المحسن
        control_frame = QFrame()
        control_frame.setObjectName("ControlFrame")
        control_layout = QVBoxLayout(control_frame)

        # الصف الأول - اختيار التقرير والفترة
        first_row = QHBoxLayout()

        first_row.addWidget(QLabel("التقرير المحاسبي:"))
        self.accounting_report_combo = QComboBox()
        self.accounting_report_combo.addItems([
            "ميزان المراجعة",
            "قائمة الدخل",
            "الميزانية العمومية",
            "تقرير التدفقات النقدية",
            "تقرير القيود المحاسبية",
            "كشف حساب تفصيلي"
        ])
        self.accounting_report_combo.setMinimumWidth(200)
        first_row.addWidget(self.accounting_report_combo)

        first_row.addWidget(QLabel("من تاريخ:"))
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        first_row.addWidget(self.from_date)

        first_row.addWidget(QLabel("إلى تاريخ:"))
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        first_row.addWidget(self.to_date)

        first_row.addStretch()
        control_layout.addLayout(first_row)

        # الصف الثاني - خيارات إضافية حسب نوع التقرير
        second_row = QHBoxLayout()

        # خيارات كشف الحساب
        second_row.addWidget(QLabel("الحساب:"))
        self.account_combo = QComboBox()
        self.account_combo.setMinimumWidth(250)
        self.account_combo.setVisible(False)
        second_row.addWidget(self.account_combo)

        # خيارات ميزان المراجعة
        second_row.addWidget(QLabel("مستوى التفصيل:"))
        self.detail_level_combo = QComboBox()
        self.detail_level_combo.addItems(["الحسابات النهائية فقط", "جميع المستويات", "المستوى الأول", "المستوى الثاني"])
        self.detail_level_combo.setVisible(False)
        second_row.addWidget(self.detail_level_combo)

        # خيار إظهار الأرصدة الصفرية
        self.show_zero_balances = QCheckBox("إظهار الأرصدة الصفرية")
        self.show_zero_balances.setVisible(False)
        second_row.addWidget(self.show_zero_balances)

        second_row.addStretch()
        control_layout.addLayout(second_row)

        # الصف الثالث - أزرار التحكم
        third_row = QHBoxLayout()

        self.generate_accounting_btn = QPushButton("📊 إنشاء التقرير")
        self.generate_accounting_btn.setObjectName("PrimaryButton")
        third_row.addWidget(self.generate_accounting_btn)

        self.export_excel_accounting_btn = QPushButton("📄 تصدير Excel")
        self.export_excel_accounting_btn.setObjectName("SuccessButton")
        third_row.addWidget(self.export_excel_accounting_btn)

        self.export_pdf_accounting_btn = QPushButton("📑 تصدير PDF")
        self.export_pdf_accounting_btn.setObjectName("InfoButton")
        third_row.addWidget(self.export_pdf_accounting_btn)

        self.print_accounting_btn = QPushButton("🖨️ طباعة")
        self.print_accounting_btn.setObjectName("WarningButton")
        third_row.addWidget(self.print_accounting_btn)

        self.refresh_accounting_btn = QPushButton("🔄 تحديث")
        self.refresh_accounting_btn.setObjectName("InfoButton")
        third_row.addWidget(self.refresh_accounting_btn)

        third_row.addStretch()
        control_layout.addLayout(third_row)

        layout.addWidget(control_frame)

        # جدول التقرير المحاسبي المحسن
        self.accounting_table = QTableWidget()
        self.accounting_table.setAlternatingRowColors(True)
        self.accounting_table.setSortingEnabled(True)
        self.accounting_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.accounting_table.setContextMenuPolicy(Qt.CustomContextMenu)
        layout.addWidget(self.accounting_table)

        # شريط معلومات التقرير
        report_info_frame = QFrame()
        report_info_frame.setObjectName("InfoFrame")
        report_info_layout = QHBoxLayout(report_info_frame)

        self.report_title_label = QLabel("اختر نوع التقرير")
        self.report_title_label.setObjectName("ReportTitle")
        report_info_layout.addWidget(self.report_title_label)

        report_info_layout.addStretch()

        self.report_date_label = QLabel("")
        self.report_total_label = QLabel("")

        report_info_layout.addWidget(self.report_date_label)
        report_info_layout.addWidget(QLabel("|"))
        report_info_layout.addWidget(self.report_total_label)

        layout.addWidget(report_info_frame)

        # ربط الأحداث
        self.accounting_report_combo.currentTextChanged.connect(self.on_accounting_report_changed)
        self.generate_accounting_btn.clicked.connect(self.generate_accounting_report)
        self.export_excel_accounting_btn.clicked.connect(self.export_accounting_excel)
        self.export_pdf_accounting_btn.clicked.connect(self.export_accounting_pdf)
        self.print_accounting_btn.clicked.connect(self.print_accounting_report)
        self.refresh_accounting_btn.clicked.connect(self.refresh_accounting_data)
        self.accounting_table.customContextMenuRequested.connect(self.show_accounting_context_menu)

        # تحديث الخيارات الأولية
        self.on_accounting_report_changed()

        self.tab_widget.addTab(self.accounting_tab, "التقارير المحاسبية المتقدمة")

    def create_detailed_reports_tab(self):
        """إنشاء تبويب التقارير التفصيلية"""
        self.detailed_tab = QWidget()
        layout = QVBoxLayout(self.detailed_tab)

        # شريط التحكم
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        # اختيار نوع التقرير التفصيلي
        control_layout.addWidget(QLabel("التقرير التفصيلي:"))
        self.detailed_report_combo = QComboBox()
        self.detailed_report_combo.addItems([
            "كشف حساب عميل",
            "كشف حساب موظف",
            "تفاصيل مشروع",
            "حركات حساب محاسبي",
            "تقرير المصروفات التفصيلي",
            "تقرير الدفعات التفصيلي"
        ])
        control_layout.addWidget(self.detailed_report_combo)

        # اختيار العنصر
        control_layout.addWidget(QLabel("اختيار:"))
        self.item_combo = QComboBox()
        control_layout.addWidget(self.item_combo)

        # فترة التقرير
        control_layout.addWidget(QLabel("من:"))
        self.detailed_from_date = QDateEdit()
        self.detailed_from_date.setDate(QDate.currentDate().addDays(-30))
        control_layout.addWidget(self.detailed_from_date)

        control_layout.addWidget(QLabel("إلى:"))
        self.detailed_to_date = QDateEdit()
        self.detailed_to_date.setDate(QDate.currentDate())
        control_layout.addWidget(self.detailed_to_date)

        # أزرار التحكم
        self.generate_detailed_btn = QPushButton("إنشاء التقرير التفصيلي")
        self.export_detailed_btn = QPushButton("تصدير")

        control_layout.addWidget(self.generate_detailed_btn)
        control_layout.addWidget(self.export_detailed_btn)
        control_layout.addStretch()

        layout.addWidget(control_frame)

        # جدول التقرير التفصيلي
        self.detailed_table = QTableWidget()
        self.detailed_table.setAlternatingRowColors(True)
        layout.addWidget(self.detailed_table)

        # ربط الأحداث
        self.detailed_report_combo.currentTextChanged.connect(self.update_item_combo)
        self.generate_detailed_btn.clicked.connect(self.generate_detailed_report)
        self.export_detailed_btn.clicked.connect(self.export_detailed_report)

        self.tab_widget.addTab(self.detailed_tab, "التقارير التفصيلية")

    def create_journal_entries_tab(self):
        """إنشاء تبويب القيود المحاسبية"""
        self.journal_tab = QWidget()
        layout = QVBoxLayout(self.journal_tab)

        # شريط التحكم العلوي
        control_frame = QFrame()
        control_frame.setObjectName("ControlFrame")
        control_layout = QHBoxLayout(control_frame)

        # فلترة القيود
        control_layout.addWidget(QLabel("من تاريخ:"))
        self.journal_from_date = QDateEdit()
        self.journal_from_date.setDate(QDate.currentDate().addDays(-30))
        self.journal_from_date.setCalendarPopup(True)
        control_layout.addWidget(self.journal_from_date)

        control_layout.addWidget(QLabel("إلى تاريخ:"))
        self.journal_to_date = QDateEdit()
        self.journal_to_date.setDate(QDate.currentDate())
        self.journal_to_date.setCalendarPopup(True)
        control_layout.addWidget(self.journal_to_date)

        control_layout.addWidget(QLabel("رقم القيد:"))
        self.journal_number_edit = QLineEdit()
        self.journal_number_edit.setPlaceholderText("اختياري")
        control_layout.addWidget(self.journal_number_edit)

        control_layout.addWidget(QLabel("نوع القيد:"))
        self.journal_type_combo = QComboBox()
        self.journal_type_combo.addItems(["الكل", "عام", "دفعة مشروع", "مصروف", "راتب", "عهدة"])
        control_layout.addWidget(self.journal_type_combo)

        control_layout.addWidget(QLabel("الحالة:"))
        self.journal_status_combo = QComboBox()
        self.journal_status_combo.addItems(["الكل", "مسودة", "مؤكد", "ملغي"])
        control_layout.addWidget(self.journal_status_combo)

        # زر التحميل
        self.load_journal_btn = QPushButton("🔄 تحميل القيود")
        self.load_journal_btn.setObjectName("PrimaryButton")
        control_layout.addWidget(self.load_journal_btn)

        control_layout.addStretch()
        layout.addWidget(control_frame)

        # شريط أزرار العمليات
        operations_frame = QFrame()
        operations_frame.setObjectName("OperationsFrame")
        operations_layout = QHBoxLayout(operations_frame)

        self.new_journal_btn = QPushButton("➕ قيد جديد")
        self.new_journal_btn.setObjectName("SuccessButton")
        operations_layout.addWidget(self.new_journal_btn)

        self.edit_journal_btn = QPushButton("✏️ تعديل قيد")
        self.edit_journal_btn.setObjectName("WarningButton")
        operations_layout.addWidget(self.edit_journal_btn)

        self.view_journal_btn = QPushButton("👁️ عرض تفاصيل")
        self.view_journal_btn.setObjectName("InfoButton")
        operations_layout.addWidget(self.view_journal_btn)

        self.approve_journal_btn = QPushButton("✅ اعتماد قيد")
        self.approve_journal_btn.setObjectName("SuccessButton")
        operations_layout.addWidget(self.approve_journal_btn)

        self.cancel_journal_btn = QPushButton("❌ إلغاء قيد")
        self.cancel_journal_btn.setObjectName("DangerButton")
        operations_layout.addWidget(self.cancel_journal_btn)

        self.delete_journal_btn = QPushButton("🗑️ حذف قيد")
        self.delete_journal_btn.setObjectName("DangerButton")
        operations_layout.addWidget(self.delete_journal_btn)

        self.print_journal_btn = QPushButton("🖨️ طباعة")
        self.print_journal_btn.setObjectName("InfoButton")
        operations_layout.addWidget(self.print_journal_btn)

        operations_layout.addStretch()
        layout.addWidget(operations_frame)

        # جدول القيود المحسن
        self.journal_table = QTableWidget()
        self.journal_table.setAlternatingRowColors(True)
        self.journal_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.journal_table.setSelectionMode(QTableWidget.SingleSelection)
        self.journal_table.setSortingEnabled(True)
        self.journal_table.setContextMenuPolicy(Qt.CustomContextMenu)
        layout.addWidget(self.journal_table)

        # معلومات إضافية
        info_frame = QFrame()
        info_frame.setObjectName("InfoFrame")
        info_layout = QHBoxLayout(info_frame)

        self.journal_count_label = QLabel("عدد القيود: 0")
        self.journal_total_debit_label = QLabel("إجمالي مدين: 0.00")
        self.journal_total_credit_label = QLabel("إجمالي دائن: 0.00")

        info_layout.addWidget(self.journal_count_label)
        info_layout.addWidget(QLabel("|"))
        info_layout.addWidget(self.journal_total_debit_label)
        info_layout.addWidget(QLabel("|"))
        info_layout.addWidget(self.journal_total_credit_label)
        info_layout.addStretch()

        layout.addWidget(info_frame)

        # ربط الأحداث
        self.load_journal_btn.clicked.connect(self.load_journal_entries)
        self.new_journal_btn.clicked.connect(self.create_new_journal_entry)
        self.edit_journal_btn.clicked.connect(self.edit_journal_entry)
        self.view_journal_btn.clicked.connect(self.view_journal_entry)
        self.approve_journal_btn.clicked.connect(self.approve_journal_entry)
        self.cancel_journal_btn.clicked.connect(self.cancel_journal_entry)
        self.delete_journal_btn.clicked.connect(self.delete_journal_entry)
        self.print_journal_btn.clicked.connect(self.print_journal_entry)
        self.journal_table.itemSelectionChanged.connect(self.on_journal_selection_changed)
        self.journal_table.customContextMenuRequested.connect(self.show_journal_context_menu)
        self.journal_table.itemDoubleClicked.connect(self.view_journal_entry)

        # تحديث حالة الأزرار
        self.update_journal_buttons_state()

        self.tab_widget.addTab(self.journal_tab, "القيود المحاسبية")

    def create_account_tree_tab(self):
        """إنشاء تبويب شجرة الحسابات"""
        self.tree_tab = QWidget()
        layout = QVBoxLayout(self.tree_tab)

        # شريط التحكم
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        # أزرار التحكم
        self.add_account_btn = QPushButton("إضافة حساب")
        self.edit_account_btn = QPushButton("تعديل حساب")
        self.delete_account_btn = QPushButton("حذف حساب")
        self.refresh_tree_btn = QPushButton("تحديث الشجرة")

        control_layout.addWidget(self.add_account_btn)
        control_layout.addWidget(self.edit_account_btn)
        control_layout.addWidget(self.delete_account_btn)
        control_layout.addWidget(self.refresh_tree_btn)
        control_layout.addStretch()

        layout.addWidget(control_frame)

        # شجرة الحسابات
        self.account_tree = QTreeWidget()
        self.account_tree.setHeaderLabels(["اسم الحساب", "كود الحساب", "نوع الحساب", "الرصيد الحالي"])
        self.account_tree.setColumnWidth(0, 300)
        self.account_tree.setColumnWidth(1, 120)
        self.account_tree.setColumnWidth(2, 120)
        self.account_tree.setColumnWidth(3, 150)
        layout.addWidget(self.account_tree)

        # ربط الأحداث
        self.add_account_btn.clicked.connect(self.add_account)
        self.edit_account_btn.clicked.connect(self.edit_account)
        self.delete_account_btn.clicked.connect(self.delete_account)
        self.refresh_tree_btn.clicked.connect(self.load_account_tree)

        self.tab_widget.addTab(self.tree_tab, "شجرة الحسابات")

    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات والرسوم البيانية"""
        self.analytics_tab = QWidget()
        layout = QVBoxLayout(self.analytics_tab)

        # شريط التحكم
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        control_layout.addWidget(QLabel("نوع التحليل:"))
        self.analytics_type_combo = QComboBox()
        self.analytics_type_combo.addItems([
            "تحليل الإيرادات الشهرية",
            "تحليل المصروفات حسب النوع",
            "تحليل ربحية المشاريع",
            "تحليل أداء العملاء",
            "مقارنة السنوات",
            "توقعات مالية"
        ])
        control_layout.addWidget(self.analytics_type_combo)

        self.generate_analytics_btn = QPushButton("إنشاء التحليل")
        control_layout.addWidget(self.generate_analytics_btn)
        control_layout.addStretch()

        layout.addWidget(control_frame)

        # منطقة عرض التحليلات
        self.analytics_area = QScrollArea()
        self.analytics_widget = QWidget()
        self.analytics_layout = QVBoxLayout(self.analytics_widget)
        self.analytics_area.setWidget(self.analytics_widget)
        self.analytics_area.setWidgetResizable(True)
        layout.addWidget(self.analytics_area)

        # ربط الأحداث
        self.generate_analytics_btn.clicked.connect(self.generate_analytics)

        self.tab_widget.addTab(self.analytics_tab, "التحليلات والرسوم البيانية")

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setObjectName("StatusFrame")
        status_frame.setFixedHeight(30)
        status_layout = QHBoxLayout(status_frame)

        self.status_label = QLabel("جاهز")
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.progress_bar)

        parent_layout.addWidget(status_frame)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.update_quick_info()
            self.load_account_tree()
            self.update_item_combo()
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")

    def apply_styles(self):
        """تطبيق الأنماط"""
        try:
            # تطبيق الأنماط الأساسية مع معالجة الأخطاء
            try:
                apply_stylesheet(self)
            except:
                pass

            apply_financial_reports_style(self)

            # أنماط مخصصة للنظام الموحد المحسن
            custom_style = """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }

            #HeaderFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }

            #MainTitle {
                color: white;
                font-size: 26px;
                font-weight: bold;
                padding: 15px;
                text-align: center;
            }

            #QuickInfoFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
            }

            #CardTitle {
                font-size: 11px;
                color: #6c757d;
                font-weight: bold;
            }

            #CardValue {
                font-size: 14px;
                font-weight: bold;
            }

            #MainTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background: white;
            }

            #MainTabWidget::tab-bar {
                alignment: right;
            }

            #MainTabWidget QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }

            #MainTabWidget QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }

            #ControlFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin: 5px;
                padding: 10px;
            }

            #OperationsFrame {
                background: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin: 5px;
                padding: 8px;
            }

            #InfoFrame {
                background: #e9ecef;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin: 5px;
                padding: 5px;
            }

            #StatusFrame {
                background: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }

            #ReportTitle {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
            }

            /* أزرار مخصصة */
            #PrimaryButton {
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }

            #PrimaryButton:hover {
                background: #0056b3;
            }

            #PrimaryButton:pressed {
                background: #004085;
            }

            #SuccessButton {
                background: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }

            #SuccessButton:hover {
                background: #1e7e34;
            }

            #SuccessButton:pressed {
                background: #155724;
            }

            #WarningButton {
                background: #ffc107;
                color: #212529;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }

            #WarningButton:hover {
                background: #e0a800;
            }

            #WarningButton:pressed {
                background: #d39e00;
            }

            #DangerButton {
                background: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }

            #DangerButton:hover {
                background: #c82333;
            }

            #DangerButton:pressed {
                background: #bd2130;
            }

            #InfoButton {
                background: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }

            #InfoButton:hover {
                background: #138496;
            }

            #InfoButton:pressed {
                background: #117a8b;
            }

            QPushButton:disabled {
                background: #6c757d;
                color: #ffffff;
                opacity: 0.6;
            }

            QTableWidget {
                gridline-color: #dee2e6;
                background: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }

            QTableWidget::item:selected {
                background: #007bff;
                color: white;
            }

            QTableWidget::item:hover {
                background: #e3f2fd;
            }

            QHeaderView::section {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }

            QComboBox, QLineEdit, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                min-height: 20px;
            }

            QComboBox:focus, QLineEdit:focus, QDateEdit:focus {
                border-color: #007bff;
                outline: none;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }

            QCheckBox {
                spacing: 5px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }

            QCheckBox::indicator:unchecked {
                border: 2px solid #ced4da;
                background: white;
                border-radius: 3px;
            }

            QCheckBox::indicator:checked {
                border: 2px solid #007bff;
                background: #007bff;
                border-radius: 3px;
            }

            QTreeWidget {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background: white;
                alternate-background-color: #f8f9fa;
            }

            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f1f3f4;
            }

            QTreeWidget::item:selected {
                background: #007bff;
                color: white;
            }

            QTreeWidget::item:hover {
                background: #e3f2fd;
            }

            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            """

            self.setStyleSheet(custom_style)

        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {e}")

    def populate_years(self, combo_box):
        """ملء قائمة السنوات"""
        try:
            current_year = QDate.currentDate().year()
            years = []

            # إضافة السنوات من 2020 إلى السنة الحالية + 2
            for year in range(2020, current_year + 3):
                years.append(str(year))

            combo_box.clear()
            combo_box.addItems(years)
            combo_box.setCurrentText(str(current_year))

        except Exception as e:
            print(f"خطأ في ملء السنوات: {e}")

    def get_db_connection(self, year=None):
        """الحصول على اتصال قاعدة البيانات"""
        try:
            if not year:
                year = QDate.currentDate().year()

            # استخدام متغيرات قاعدة البيانات مع قيم افتراضية
            try:
                host = DB_HOST if 'DB_HOST' in globals() else 'localhost'
                user = DEFAULT_DB_USER if 'DEFAULT_DB_USER' in globals() else 'root'
                password = DEFAULT_DB_PASSWORD if 'DEFAULT_DB_PASSWORD' in globals() else ''
                database = f"project_manager_V2"
            except:
                host = 'localhost'
                user = 'root'
                password = ''
                database = 'project_manager_V2'

            conn = mysql.connector.connect(
                host=host,
                user=user,
                password=password,
                database=database
            )
            return conn
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None

    def update_quick_info(self):
        """تحديث المعلومات السريعة"""
        try:
            year = self.year_combo.currentText() if hasattr(self, 'year_combo') else str(QDate.currentDate().year())
            conn = self.get_db_connection(year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # حساب إجمالي الإيرادات
            cursor.execute("""
                SELECT COALESCE(SUM(المدفوع), 0) as total_revenue
                FROM المشاريع
            """)
            revenue_result = cursor.fetchone()
            total_revenue = revenue_result['total_revenue'] if revenue_result else 0

            # حساب إجمالي المصروفات
            cursor.execute("""
                SELECT COALESCE(SUM(المبلغ), 0) as total_expenses
                FROM الحسابات
            """)
            expenses_result = cursor.fetchone()
            total_expenses = expenses_result['total_expenses'] if expenses_result else 0

            # حساب المبالغ المعلقة
            cursor.execute("""
                SELECT COALESCE(SUM(الباقي), 0) as pending_amount
                FROM المشاريع
                WHERE الباقي > 0
            """)
            pending_result = cursor.fetchone()
            pending_amount = pending_result['pending_amount'] if pending_result else 0

            # حساب صافي الربح
            net_profit = total_revenue - total_expenses

            # تحديث البطاقات
            self.update_info_card_value(self.total_revenue_card, f"{total_revenue:,.2f}")
            self.update_info_card_value(self.total_expenses_card, f"{total_expenses:,.2f}")
            self.update_info_card_value(self.net_profit_card, f"{net_profit:,.2f}")
            self.update_info_card_value(self.pending_amount_card, f"{pending_amount:,.2f}")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث المعلومات السريعة: {e}")



    # ===== وظائف التقارير المالية =====

    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        try:
            self.status_label.setText("جاري إنشاء التقرير المالي...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            report_type = self.report_type_combo.currentText()
            year = self.year_combo.currentText()

            if report_type == "ملخص مالي شامل":
                self.generate_financial_summary(year)
            elif report_type == "تقرير المشاريع المالي":
                self.generate_projects_financial_report(year)
            elif report_type == "تقرير العملاء المالي":
                self.generate_clients_financial_report(year)
            elif report_type == "تقرير الموظفين المالي":
                self.generate_employees_financial_report(year)
            elif report_type == "تقرير التدفق النقدي":
                self.generate_cash_flow_report(year)
            elif report_type == "مقارنة السنوات":
                self.generate_years_comparison()

            self.progress_bar.setValue(100)
            self.status_label.setText("تم إنشاء التقرير بنجاح")

            # إخفاء شريط التقدم بعد 3 ثوان
            QTimer.singleShot(3000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.status_label.setText("خطأ في إنشاء التقرير")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير المالي:\n{str(e)}")

    def generate_financial_summary(self, year):
        """إنشاء الملخص المالي الشامل"""
        try:
            conn = self.get_db_connection(year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["البيان", "القيمة", "النسبة المئوية", "ملاحظات"]
            self.financial_table.setColumnCount(len(headers))
            self.financial_table.setHorizontalHeaderLabels(headers)

            # جمع البيانات المالية
            financial_data = []

            # إجمالي الإيرادات
            cursor.execute("SELECT COALESCE(SUM(المدفوع), 0) as total FROM المشاريع")
            total_revenue = cursor.fetchone()['total']
            financial_data.append(["إجمالي الإيرادات", f"{total_revenue:,.2f}", "100%", "من جميع المشاريع"])

            # إجمالي المصروفات
            cursor.execute("SELECT COALESCE(SUM(المبلغ), 0) as total FROM الحسابات")
            total_expenses = cursor.fetchone()['total']
            expense_percentage = (total_expenses / total_revenue * 100) if total_revenue > 0 else 0
            financial_data.append(["إجمالي المصروفات", f"{total_expenses:,.2f}", f"{expense_percentage:.1f}%", "جميع أنواع المصروفات"])

            # صافي الربح
            net_profit = total_revenue - total_expenses
            profit_percentage = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
            financial_data.append(["صافي الربح", f"{net_profit:,.2f}", f"{profit_percentage:.1f}%", "الإيرادات - المصروفات"])

            # المبالغ المعلقة
            cursor.execute("SELECT COALESCE(SUM(الباقي), 0) as total FROM المشاريع WHERE الباقي > 0")
            pending_amount = cursor.fetchone()['total']
            pending_percentage = (pending_amount / total_revenue * 100) if total_revenue > 0 else 0
            financial_data.append(["المبالغ المعلقة", f"{pending_amount:,.2f}", f"{pending_percentage:.1f}%", "مستحقات غير محصلة"])

            # عدد المشاريع
            cursor.execute("SELECT COUNT(*) as count FROM المشاريع")
            projects_count = cursor.fetchone()['count']
            financial_data.append(["عدد المشاريع", str(projects_count), "-", "إجمالي المشاريع"])

            # متوسط قيمة المشروع
            avg_project_value = total_revenue / projects_count if projects_count > 0 else 0
            financial_data.append(["متوسط قيمة المشروع", f"{avg_project_value:,.2f}", "-", "الإيرادات ÷ عدد المشاريع"])

            # ملء الجدول
            self.financial_table.setRowCount(len(financial_data))
            for row, data in enumerate(financial_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الصفوف حسب النوع
                    if "ربح" in data[0]:
                        if net_profit > 0:
                            item.setForeground(QColor("#d4edda"))  # أخضر فاتح
                        else:
                            item.setForeground(QColor("#f8d7da"))  # أحمر فاتح
                    elif "مصروفات" in data[0]:
                        item.setForeground(QColor("#fff3cd"))  # أصفر فاتح
                    elif "إيرادات" in data[0]:
                        item.setForeground(QColor("#d1ecf1"))  # أزرق فاتح

                    self.financial_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.financial_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(3, QHeaderView.Stretch)

            cursor.close()
            conn.close()

            self.progress_bar.setValue(50)

        except Exception as e:
            print(f"خطأ في إنشاء الملخص المالي: {e}")
            raise e

    def generate_projects_financial_report(self, year):
        """إنشاء تقرير المشاريع المالي"""
        try:
            conn = self.get_db_connection(year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["اسم المشروع", "العميل", "المبلغ الإجمالي", "المدفوع", "الباقي", "النسبة المدفوعة", "الحالة"]
            self.financial_table.setColumnCount(len(headers))
            self.financial_table.setHorizontalHeaderLabels(headers)

            # جلب بيانات المشاريع
            cursor.execute("""
                SELECT p.اسم_المشروع, c.اسم_العميل, p.المبلغ, p.المدفوع, p.الباقي, p.الحالة
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                ORDER BY p.المبلغ DESC
            """)

            projects = cursor.fetchall()
            self.financial_table.setRowCount(len(projects))

            for row, project in enumerate(projects):
                # حساب النسبة المدفوعة
                paid_percentage = (project['المدفوع'] / project['المبلغ'] * 100) if project['المبلغ'] > 0 else 0

                data = [
                    project['اسم_المشروع'] or "غير محدد",
                    project['اسم_العميل'] or "غير محدد",
                    f"{project['المبلغ']:,.2f}",
                    f"{project['المدفوع']:,.2f}",
                    f"{project['الباقي']:,.2f}",
                    f"{paid_percentage:.1f}%",
                    project['الحالة'] or "غير محدد"
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب الحالة
                    if col == 6:  # عمود الحالة
                        if "مكتمل" in value:
                            item.setForeground(QColor("#d4edda"))
                        elif "قيد الإنجاز" in value:
                            item.setForeground(QColor("#fff3cd"))
                        elif "متوقف" in value:
                            item.setForeground(QColor("#f8d7da"))

                    self.financial_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.financial_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            for i in range(2, 7):
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المشاريع المالي: {e}")
            raise e

    def generate_clients_financial_report(self, year):
        """إنشاء تقرير العملاء المالي"""
        try:
            conn = self.get_db_connection(year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["اسم العميل", "عدد المشاريع", "إجمالي المبالغ", "إجمالي المدفوع", "إجمالي الباقي", "نسبة السداد"]
            self.financial_table.setColumnCount(len(headers))
            self.financial_table.setHorizontalHeaderLabels(headers)

            # جلب بيانات العملاء
            cursor.execute("""
                SELECT
                    c.اسم_العميل,
                    COUNT(p.id) as عدد_المشاريع,
                    COALESCE(SUM(p.المبلغ), 0) as إجمالي_المبالغ,
                    COALESCE(SUM(p.المدفوع), 0) as إجمالي_المدفوع,
                    COALESCE(SUM(p.الباقي), 0) as إجمالي_الباقي
                FROM العملاء c
                LEFT JOIN المشاريع p ON c.id = p.معرف_العميل
                GROUP BY c.id, c.اسم_العميل
                HAVING عدد_المشاريع > 0
                ORDER BY إجمالي_المبالغ DESC
            """)

            clients = cursor.fetchall()
            self.financial_table.setRowCount(len(clients))

            for row, client in enumerate(clients):
                # حساب نسبة السداد
                payment_percentage = (client['إجمالي_المدفوع'] / client['إجمالي_المبالغ'] * 100) if client['إجمالي_المبالغ'] > 0 else 0

                data = [
                    client['اسم_العميل'] or "غير محدد",
                    str(client['عدد_المشاريع']),
                    f"{client['إجمالي_المبالغ']:,.2f}",
                    f"{client['إجمالي_المدفوع']:,.2f}",
                    f"{client['إجمالي_الباقي']:,.2f}",
                    f"{payment_percentage:.1f}%"
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب نسبة السداد
                    if col == 5:  # عمود نسبة السداد
                        if payment_percentage >= 90:
                            item.setForeground(QColor("#d4edda"))  # أخضر
                        elif payment_percentage >= 70:
                            item.setForeground(QColor("#fff3cd"))  # أصفر
                        else:
                            item.setForeground(QColor("#f8d7da"))  # أحمر

                    self.financial_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.financial_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)
            for i in range(1, 6):
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء تقرير العملاء المالي: {e}")
            raise e

    def generate_employees_financial_report(self, year):
        """إنشاء تقرير الموظفين المالي"""
        try:
            conn = self.get_db_connection(year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["اسم الموظف", "الوظيفة", "المرتب", "الرصيد", "السحب", "الصافي", "عدد المعاملات"]
            self.financial_table.setColumnCount(len(headers))
            self.financial_table.setHorizontalHeaderLabels(headers)

            # جلب بيانات الموظفين
            cursor.execute("""
                SELECT
                    m.اسم_الموظف, m.الوظيفة, m.المرتب, m.الرصيد, m.السحب,
                    (m.الرصيد - m.السحب) as الصافي,
                    COUNT(t.id) as عدد_المعاملات
                FROM الموظفين m
                LEFT JOIN الموظفين_معاملات_مالية t ON m.id = t.معرف_الموظف
                GROUP BY m.id
                ORDER BY m.المرتب DESC
            """)

            employees = cursor.fetchall()
            self.financial_table.setRowCount(len(employees))

            for row, employee in enumerate(employees):
                data = [
                    employee['اسم_الموظف'] or "غير محدد",
                    employee['الوظيفة'] or "غير محدد",
                    f"{employee['المرتب']:,.2f}",
                    f"{employee['الرصيد']:,.2f}",
                    f"{employee['السحب']:,.2f}",
                    f"{employee['الصافي']:,.2f}",
                    str(employee['عدد_المعاملات'])
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب الصافي
                    if col == 5:  # عمود الصافي
                        net_amount = employee['الصافي']
                        if net_amount > 0:
                            item.setForeground(QColor("#d4edda"))  # أخضر
                        elif net_amount == 0:
                            item.setForeground(QColor("#fff3cd"))  # أصفر
                        else:
                            item.setForeground(QColor("#f8d7da"))  # أحمر

                    self.financial_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.financial_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            for i in range(2, 7):
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الموظفين المالي: {e}")
            raise e

    def generate_cash_flow_report(self, year):
        """إنشاء تقرير التدفق النقدي"""
        try:
            conn = self.get_db_connection(year)
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["الشهر", "الإيرادات", "المصروفات", "صافي التدفق", "التدفق التراكمي"]
            self.financial_table.setColumnCount(len(headers))
            self.financial_table.setHorizontalHeaderLabels(headers)

            # جلب البيانات الشهرية
            months_data = []
            cumulative_flow = 0

            for month in range(1, 13):
                # الإيرادات الشهرية
                cursor.execute("""
                    SELECT COALESCE(SUM(المبلغ_المدفوع), 0) as monthly_revenue
                    FROM المشاريع_المدفوعات
                    WHERE YEAR(تاريخ_الدفع) = %s AND MONTH(تاريخ_الدفع) = %s
                """, (year, month))
                revenue_result = cursor.fetchone()
                monthly_revenue = revenue_result['monthly_revenue'] if revenue_result else 0

                # المصروفات الشهرية
                cursor.execute("""
                    SELECT COALESCE(SUM(المبلغ), 0) as monthly_expenses
                    FROM الحسابات
                    WHERE YEAR(تاريخ_المصروف) = %s AND MONTH(تاريخ_المصروف) = %s
                """, (year, month))
                expenses_result = cursor.fetchone()
                monthly_expenses = expenses_result['monthly_expenses'] if expenses_result else 0

                # صافي التدفق
                net_flow = monthly_revenue - monthly_expenses
                cumulative_flow += net_flow

                # أسماء الشهور بالعربية
                month_names = [
                    "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                    "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
                ]

                months_data.append([
                    month_names[month-1],
                    f"{monthly_revenue:,.2f}",
                    f"{monthly_expenses:,.2f}",
                    f"{net_flow:,.2f}",
                    f"{cumulative_flow:,.2f}"
                ])

            # ملء الجدول
            self.financial_table.setRowCount(len(months_data))
            for row, data in enumerate(months_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب صافي التدفق
                    if col == 3:  # عمود صافي التدفق
                        try:
                            flow_value = float(value.replace(',', ''))
                            if flow_value > 0:
                                item.setForeground(QColor("#d4edda"))  # أخضر
                            elif flow_value == 0:
                                item.setForeground(QColor("#fff3cd"))  # أصفر
                            else:
                                item.setForeground(QColor("#f8d7da"))  # أحمر
                        except:
                            pass

                    self.financial_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.financial_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            for i in range(1, 5):
                header.setSectionResizeMode(i, QHeaderView.Stretch)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء تقرير التدفق النقدي: {e}")
            raise e

    def generate_years_comparison(self):
        """إنشاء مقارنة السنوات"""
        try:
            # إعداد الجدول
            headers = ["السنة", "الإيرادات", "المصروفات", "صافي الربح", "عدد المشاريع", "متوسط قيمة المشروع"]
            self.financial_table.setColumnCount(len(headers))
            self.financial_table.setHorizontalHeaderLabels(headers)

            current_year = QDate.currentDate().year()
            years_data = []

            # جمع بيانات آخر 5 سنوات
            for year in range(current_year - 4, current_year + 1):
                conn = self.get_db_connection(str(year))
                if not conn:
                    continue

                cursor = conn.cursor(dictionary=True)

                try:
                    # الإيرادات
                    cursor.execute("SELECT COALESCE(SUM(المدفوع), 0) as total FROM المشاريع")
                    revenue = cursor.fetchone()['total']

                    # المصروفات
                    cursor.execute("SELECT COALESCE(SUM(المبلغ), 0) as total FROM الحسابات")
                    expenses = cursor.fetchone()['total']

                    # عدد المشاريع
                    cursor.execute("SELECT COUNT(*) as count FROM المشاريع")
                    projects_count = cursor.fetchone()['count']

                    # حسابات
                    net_profit = revenue - expenses
                    avg_project_value = revenue / projects_count if projects_count > 0 else 0

                    years_data.append([
                        str(year),
                        f"{revenue:,.2f}",
                        f"{expenses:,.2f}",
                        f"{net_profit:,.2f}",
                        str(projects_count),
                        f"{avg_project_value:,.2f}"
                    ])

                except Exception as e:
                    print(f"خطأ في جلب بيانات السنة {year}: {e}")
                finally:
                    cursor.close()
                    conn.close()

            # ملء الجدول
            self.financial_table.setRowCount(len(years_data))
            for row, data in enumerate(years_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب صافي الربح
                    if col == 3:  # عمود صافي الربح
                        try:
                            profit_value = float(value.replace(',', ''))
                            if profit_value > 0:
                                item.setForeground(QColor("#d4edda"))  # أخضر
                            elif profit_value == 0:
                                item.setForeground(QColor("#fff3cd"))  # أصفر
                            else:
                                item.setForeground(QColor("#f8d7da"))  # أحمر
                        except:
                            pass

                    self.financial_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.financial_table.horizontalHeader()
            for i in range(6):
                header.setSectionResizeMode(i, QHeaderView.Stretch)

        except Exception as e:
            print(f"خطأ في إنشاء مقارنة السنوات: {e}")
            raise e

    # ===== وظائف التقارير المحاسبية =====

    def generate_accounting_report(self):
        """إنشاء التقرير المحاسبي"""
        try:
            self.status_label.setText("جاري إنشاء التقرير المحاسبي...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            report_type = self.accounting_report_combo.currentText()
            from_date = self.from_date.date().toString("yyyy-MM-dd")
            to_date = self.to_date.date().toString("yyyy-MM-dd")

            if report_type == "ميزان المراجعة":
                self.generate_trial_balance(from_date, to_date)
            elif report_type == "قائمة الدخل":
                self.generate_income_statement(from_date, to_date)
            elif report_type == "الميزانية العمومية":
                self.generate_balance_sheet(from_date, to_date)
            elif report_type == "تقرير التدفقات النقدية":
                self.generate_cash_flow_statement(from_date, to_date)
            elif report_type == "تقرير القيود المحاسبية":
                self.generate_journal_entries_report(from_date, to_date)
            elif report_type == "كشف حساب تفصيلي":
                self.generate_account_statement(from_date, to_date)

            self.progress_bar.setValue(100)
            self.status_label.setText("تم إنشاء التقرير المحاسبي بنجاح")

            # إخفاء شريط التقدم بعد 3 ثوان
            QTimer.singleShot(3000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.status_label.setText("خطأ في إنشاء التقرير المحاسبي")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير المحاسبي:\n{str(e)}")

    def generate_trial_balance(self, from_date, to_date):
        """إنشاء ميزان المراجعة"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["كود الحساب", "اسم الحساب", "مدين", "دائن", "الرصيد"]
            self.accounting_table.setColumnCount(len(headers))
            self.accounting_table.setHorizontalHeaderLabels(headers)

            # جلب بيانات الحسابات مع الحركات
            cursor.execute("""
                SELECT
                    ش.كود_الحساب,
                    ش.اسم_الحساب,
                    COALESCE(SUM(ح.مدين), 0) as إجمالي_مدين,
                    COALESCE(SUM(ح.دائن), 0) as إجمالي_دائن,
                    ش.الرصيد_الحالي
                FROM شجرة_الحسابات ش
                LEFT JOIN حركات_الحسابات ح ON ش.كود_الحساب = ح.كود_الحساب
                    AND ح.تاريخ_القيد BETWEEN %s AND %s
                WHERE ش.حساب_نهائي = TRUE
                GROUP BY ش.كود_الحساب, ش.اسم_الحساب, ش.الرصيد_الحالي
                ORDER BY ش.كود_الحساب
            """, (from_date, to_date))

            accounts = cursor.fetchall()
            self.accounting_table.setRowCount(len(accounts))

            total_debit = 0
            total_credit = 0

            for row, account in enumerate(accounts):
                debit = account['إجمالي_مدين']
                credit = account['إجمالي_دائن']
                balance = account['الرصيد_الحالي']

                total_debit += debit
                total_credit += credit

                data = [
                    account['كود_الحساب'],
                    account['اسم_الحساب'],
                    f"{debit:,.2f}",
                    f"{credit:,.2f}",
                    f"{balance:,.2f}"
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب الرصيد
                    if col == 4:  # عمود الرصيد
                        if balance > 0:
                            item.setForeground(QColor("#d4edda"))  # أخضر
                        elif balance == 0:
                            item.setForeground(QColor("#fff3cd"))  # أصفر
                        else:
                            item.setForeground(QColor("#f8d7da"))  # أحمر

                    self.accounting_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = self.accounting_table.rowCount()
            self.accounting_table.setRowCount(totals_row + 1)

            totals_data = ["", "الإجماليات", f"{total_debit:,.2f}", f"{total_credit:,.2f}", ""]
            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#e9ecef"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.accounting_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.accounting_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.Stretch)
            for i in range(2, 5):
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء ميزان المراجعة: {e}")
            raise e

    # ===== وظائف أساسية أخرى =====

    def generate_income_statement(self, from_date, to_date):
        """إنشاء قائمة الدخل"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["البيان", "المبلغ", "النسبة المئوية"]
            self.accounting_table.setColumnCount(len(headers))
            self.accounting_table.setHorizontalHeaderLabels(headers)

            income_data = []

            # الإيرادات
            cursor.execute("""
                SELECT COALESCE(SUM(ح.دائن - ح.مدين), 0) as total_revenue
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'إيرادات'
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            total_revenue = cursor.fetchone()['total_revenue'] or 0

            # تكلفة البضاعة المباعة
            cursor.execute("""
                SELECT COALESCE(SUM(ح.مدين - ح.دائن), 0) as cost_of_goods
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'تكلفة البضاعة المباعة'
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            cost_of_goods = cursor.fetchone()['cost_of_goods'] or 0

            # إجمالي الربح
            gross_profit = total_revenue - cost_of_goods

            # المصروفات التشغيلية
            cursor.execute("""
                SELECT COALESCE(SUM(ح.مدين - ح.دائن), 0) as operating_expenses
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب IN ('مصروفات تشغيلية', 'مصروفات إدارية', 'مصروفات بيع وتسويق')
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            operating_expenses = cursor.fetchone()['operating_expenses'] or 0

            # صافي الربح التشغيلي
            operating_profit = gross_profit - operating_expenses

            # الإيرادات الأخرى
            cursor.execute("""
                SELECT COALESCE(SUM(ح.دائن - ح.مدين), 0) as other_income
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'إيرادات أخرى'
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            other_income = cursor.fetchone()['other_income'] or 0

            # المصروفات الأخرى
            cursor.execute("""
                SELECT COALESCE(SUM(ح.مدين - ح.دائن), 0) as other_expenses
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'مصروفات أخرى'
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            other_expenses = cursor.fetchone()['other_expenses'] or 0

            # صافي الربح
            net_profit = operating_profit + other_income - other_expenses

            # بناء البيانات
            income_data = [
                ["الإيرادات", f"{total_revenue:,.2f}", "100.0%"],
                ["", "", ""],
                ["تكلفة البضاعة المباعة", f"({cost_of_goods:,.2f})", f"({cost_of_goods/total_revenue*100:.1f}%)" if total_revenue > 0 else "0.0%"],
                ["", "", ""],
                ["إجمالي الربح", f"{gross_profit:,.2f}", f"{gross_profit/total_revenue*100:.1f}%" if total_revenue > 0 else "0.0%"],
                ["", "", ""],
                ["المصروفات التشغيلية:", "", ""],
                ["  - مصروفات إدارية وعمومية", f"({operating_expenses:,.2f})", f"({operating_expenses/total_revenue*100:.1f}%)" if total_revenue > 0 else "0.0%"],
                ["", "", ""],
                ["صافي الربح التشغيلي", f"{operating_profit:,.2f}", f"{operating_profit/total_revenue*100:.1f}%" if total_revenue > 0 else "0.0%"],
                ["", "", ""],
                ["الإيرادات الأخرى", f"{other_income:,.2f}", f"{other_income/total_revenue*100:.1f}%" if total_revenue > 0 else "0.0%"],
                ["المصروفات الأخرى", f"({other_expenses:,.2f})", f"({other_expenses/total_revenue*100:.1f}%)" if total_revenue > 0 else "0.0%"],
                ["", "", ""],
                ["صافي الربح", f"{net_profit:,.2f}", f"{net_profit/total_revenue*100:.1f}%" if total_revenue > 0 else "0.0%"]
            ]

            # ملء الجدول
            self.accounting_table.setRowCount(len(income_data))
            for row, data in enumerate(income_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تنسيق خاص للعناوين والإجماليات
                    if data[0] in ["الإيرادات", "إجمالي الربح", "صافي الربح التشغيلي", "صافي الربح"]:
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                        if "ربح" in data[0]:
                            if net_profit > 0:
                                item.setForeground(QColor("#d4edda"))
                            else:
                                item.setForeground(QColor("#f8d7da"))
                    elif data[0] == "":
                        item.setForeground(QColor("#f8f9fa"))

                    self.accounting_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.accounting_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)

            # تحديث معلومات التقرير
            self.report_title_label.setText("قائمة الدخل")
            self.report_date_label.setText(f"من {from_date} إلى {to_date}")
            self.report_total_label.setText(f"صافي الربح: {net_profit:,.2f}")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء قائمة الدخل: {e}")
            raise e

    def generate_balance_sheet(self, from_date, to_date):
        """إنشاء الميزانية العمومية"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["البيان", "المبلغ", "البيان", "المبلغ"]
            self.accounting_table.setColumnCount(len(headers))
            self.accounting_table.setHorizontalHeaderLabels(headers)

            # الأصول المتداولة
            cursor.execute("""
                SELECT COALESCE(SUM(ح.مدين - ح.دائن), 0) as current_assets
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'أصول متداولة'
                AND ح.تاريخ_القيد <= %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (to_date,))
            current_assets = cursor.fetchone()['current_assets'] or 0

            # الأصول الثابتة
            cursor.execute("""
                SELECT COALESCE(SUM(ح.مدين - ح.دائن), 0) as fixed_assets
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'أصول ثابتة'
                AND ح.تاريخ_القيد <= %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (to_date,))
            fixed_assets = cursor.fetchone()['fixed_assets'] or 0

            # إجمالي الأصول
            total_assets = current_assets + fixed_assets

            # الخصوم المتداولة
            cursor.execute("""
                SELECT COALESCE(SUM(ح.دائن - ح.مدين), 0) as current_liabilities
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'خصوم متداولة'
                AND ح.تاريخ_القيد <= %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (to_date,))
            current_liabilities = cursor.fetchone()['current_liabilities'] or 0

            # الخصوم طويلة الأجل
            cursor.execute("""
                SELECT COALESCE(SUM(ح.دائن - ح.مدين), 0) as long_term_liabilities
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'خصوم طويلة الأجل'
                AND ح.تاريخ_القيد <= %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (to_date,))
            long_term_liabilities = cursor.fetchone()['long_term_liabilities'] or 0

            # حقوق الملكية
            cursor.execute("""
                SELECT COALESCE(SUM(ح.دائن - ح.مدين), 0) as equity
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ش.نوع_الحساب = 'حقوق ملكية'
                AND ح.تاريخ_القيد <= %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (to_date,))
            equity = cursor.fetchone()['equity'] or 0

            # إجمالي الخصوم وحقوق الملكية
            total_liabilities_equity = current_liabilities + long_term_liabilities + equity

            # بناء البيانات
            balance_data = [
                ["الأصول", "", "الخصوم وحقوق الملكية", ""],
                ["", "", "", ""],
                ["الأصول المتداولة:", f"{current_assets:,.2f}", "الخصوم المتداولة:", f"{current_liabilities:,.2f}"],
                ["الأصول الثابتة:", f"{fixed_assets:,.2f}", "الخصوم طويلة الأجل:", f"{long_term_liabilities:,.2f}"],
                ["", "", "حقوق الملكية:", f"{equity:,.2f}"],
                ["", "", "", ""],
                ["إجمالي الأصول:", f"{total_assets:,.2f}", "إجمالي الخصوم وحقوق الملكية:", f"{total_liabilities_equity:,.2f}"]
            ]

            # ملء الجدول
            self.accounting_table.setRowCount(len(balance_data))
            for row, data in enumerate(balance_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تنسيق خاص للعناوين والإجماليات
                    if "إجمالي" in value or value in ["الأصول", "الخصوم وحقوق الملكية"]:
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                        item.setForeground(QColor("#e9ecef"))
                    elif value == "":
                        item.setForeground(QColor("#f8f9fa"))

                    self.accounting_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.accounting_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

            # تحديث معلومات التقرير
            self.report_title_label.setText("الميزانية العمومية")
            self.report_date_label.setText(f"كما في {to_date}")
            self.report_total_label.setText(f"إجمالي الأصول: {total_assets:,.2f}")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء الميزانية العمومية: {e}")
            raise e

    def generate_cash_flow_statement(self, from_date, to_date):
        """إنشاء تقرير التدفقات النقدية المحاسبي"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["البيان", "المبلغ"]
            self.accounting_table.setColumnCount(len(headers))
            self.accounting_table.setHorizontalHeaderLabels(headers)

            # التدفقات النقدية من الأنشطة التشغيلية
            cursor.execute("""
                SELECT COALESCE(SUM(
                    CASE WHEN ش.نوع_الحساب IN ('نقدية', 'بنوك') THEN ح.مدين - ح.دائن ELSE 0 END
                ), 0) as operating_cash_flow
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                JOIN القيود_المحاسبية ق ON ح.رقم_القيد = ق.رقم_القيد
                WHERE ق.نوع_القيد IN ('دفعة مشروع', 'مصروف', 'راتب')
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            operating_cash_flow = cursor.fetchone()['operating_cash_flow'] or 0

            # التدفقات النقدية من الأنشطة الاستثمارية
            cursor.execute("""
                SELECT COALESCE(SUM(
                    CASE WHEN ش.نوع_الحساب IN ('نقدية', 'بنوك') THEN ح.مدين - ح.دائن ELSE 0 END
                ), 0) as investing_cash_flow
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                JOIN القيود_المحاسبية ق ON ح.رقم_القيد = ق.رقم_القيد
                WHERE ق.نوع_القيد = 'استثمار'
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            investing_cash_flow = cursor.fetchone()['investing_cash_flow'] or 0

            # التدفقات النقدية من الأنشطة التمويلية
            cursor.execute("""
                SELECT COALESCE(SUM(
                    CASE WHEN ش.نوع_الحساب IN ('نقدية', 'بنوك') THEN ح.مدين - ح.دائن ELSE 0 END
                ), 0) as financing_cash_flow
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                JOIN القيود_المحاسبية ق ON ح.رقم_القيد = ق.رقم_القيد
                WHERE ق.نوع_القيد = 'تمويل'
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (from_date, to_date))
            financing_cash_flow = cursor.fetchone()['financing_cash_flow'] or 0

            # صافي التغير في النقدية
            net_cash_change = operating_cash_flow + investing_cash_flow + financing_cash_flow

            # بناء البيانات
            cash_flow_data = [
                ["التدفقات النقدية من الأنشطة التشغيلية", f"{operating_cash_flow:,.2f}"],
                ["التدفقات النقدية من الأنشطة الاستثمارية", f"{investing_cash_flow:,.2f}"],
                ["التدفقات النقدية من الأنشطة التمويلية", f"{financing_cash_flow:,.2f}"],
                ["", ""],
                ["صافي التغير في النقدية", f"{net_cash_change:,.2f}"]
            ]

            # ملء الجدول
            self.accounting_table.setRowCount(len(cash_flow_data))
            for row, data in enumerate(cash_flow_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تنسيق خاص للإجماليات
                    if "صافي" in data[0]:
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                        if net_cash_change > 0:
                            item.setForeground(QColor("#d4edda"))
                        else:
                            item.setForeground(QColor("#f8d7da"))
                    elif data[0] == "":
                        item.setForeground(QColor("#f8f9fa"))

                    self.accounting_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.accounting_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)

            # تحديث معلومات التقرير
            self.report_title_label.setText("تقرير التدفقات النقدية")
            self.report_date_label.setText(f"من {from_date} إلى {to_date}")
            self.report_total_label.setText(f"صافي التغير: {net_cash_change:,.2f}")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء تقرير التدفقات النقدية: {e}")
            raise e

    def generate_journal_entries_report(self, from_date, to_date):
        """إنشاء تقرير القيود المحاسبية"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["رقم القيد", "التاريخ", "اسم الحساب", "وصف الحركة", "مدين", "دائن", "المرجع"]
            self.accounting_table.setColumnCount(len(headers))
            self.accounting_table.setHorizontalHeaderLabels(headers)

            # جلب القيود
            cursor.execute("""
                SELECT ح.رقم_القيد, ح.تاريخ_القيد, ش.اسم_الحساب, ح.وصف_الحركة,
                       ح.مدين, ح.دائن, ح.المرجع
                FROM حركات_الحسابات ح
                JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ح.تاريخ_القيد BETWEEN %s AND %s
                ORDER BY ح.تاريخ_القيد, ح.رقم_القيد, ح.id
            """, (from_date, to_date))

            entries = cursor.fetchall()
            self.accounting_table.setRowCount(len(entries))

            for row, entry in enumerate(entries):
                data = [
                    entry['رقم_القيد'],
                    entry['تاريخ_القيد'].strftime("%Y-%m-%d") if entry['تاريخ_القيد'] else "",
                    entry['اسم_الحساب'],
                    entry['وصف_الحركة'] or "",
                    f"{entry['مدين']:,.2f}" if entry['مدين'] else "0.00",
                    f"{entry['دائن']:,.2f}" if entry['دائن'] else "0.00",
                    entry['المرجع'] or ""
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.accounting_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.accounting_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.Stretch)
            for i in range(4, 7):
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء تقرير القيود: {e}")
            raise e

    def generate_account_statement(self, from_date, to_date):
        """إنشاء كشف حساب تفصيلي"""
        try:
            # التحقق من اختيار الحساب
            if not hasattr(self, 'account_combo') or not self.account_combo.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب لإنشاء كشف الحساب")
                return

            account_code = self.account_combo.currentData()
            account_name = self.account_combo.currentText().split(' - ')[1] if ' - ' in self.account_combo.currentText() else self.account_combo.currentText()

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["التاريخ", "رقم القيد", "وصف الحركة", "مدين", "دائن", "الرصيد"]
            self.accounting_table.setColumnCount(len(headers))
            self.accounting_table.setHorizontalHeaderLabels(headers)

            # جلب الرصيد الافتتاحي
            cursor.execute("""
                SELECT COALESCE(SUM(ح.مدين - ح.دائن), 0) as opening_balance
                FROM حركات_الحسابات ح
                WHERE ح.كود_الحساب = %s
                AND ح.تاريخ_القيد < %s
                AND ح.حالة_الحركة != 'ملغية'
            """, (account_code, from_date))
            opening_balance = cursor.fetchone()['opening_balance'] or 0

            # جلب حركات الحساب
            cursor.execute("""
                SELECT
                    ح.تاريخ_القيد,
                    ح.رقم_القيد,
                    ح.وصف_الحركة,
                    ح.مدين,
                    ح.دائن,
                    ق.نوع_القيد
                FROM حركات_الحسابات ح
                JOIN القيود_المحاسبية ق ON ح.رقم_القيد = ق.رقم_القيد
                WHERE ح.كود_الحساب = %s
                AND ح.تاريخ_القيد BETWEEN %s AND %s
                AND ح.حالة_الحركة != 'ملغية'
                ORDER BY ح.تاريخ_القيد, ح.رقم_القيد
            """, (account_code, from_date, to_date))

            movements = cursor.fetchall()

            # إعداد البيانات
            statement_data = []
            running_balance = opening_balance

            # إضافة الرصيد الافتتاحي
            if opening_balance != 0:
                statement_data.append([
                    from_date,
                    "",
                    "الرصيد الافتتاحي",
                    f"{opening_balance:,.2f}" if opening_balance > 0 else "0.00",
                    f"{abs(opening_balance):,.2f}" if opening_balance < 0 else "0.00",
                    f"{running_balance:,.2f}"
                ])

            # إضافة الحركات
            for movement in movements:
                debit = movement['مدين'] or 0
                credit = movement['دائن'] or 0
                running_balance += debit - credit

                statement_data.append([
                    movement['تاريخ_القيد'].strftime("%Y-%m-%d") if movement['تاريخ_القيد'] else "",
                    movement['رقم_القيد'],
                    movement['وصف_الحركة'] or f"قيد {movement['نوع_القيد'] or 'عام'}",
                    f"{debit:,.2f}" if debit > 0 else "0.00",
                    f"{credit:,.2f}" if credit > 0 else "0.00",
                    f"{running_balance:,.2f}"
                ])

            # إضافة الرصيد الختامي
            if statement_data:
                statement_data.append([
                    to_date,
                    "",
                    "الرصيد الختامي",
                    "",
                    "",
                    f"{running_balance:,.2f}"
                ])

            # ملء الجدول
            self.accounting_table.setRowCount(len(statement_data))
            for row, data in enumerate(statement_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تنسيق خاص للرصيد الافتتاحي والختامي
                    if "الرصيد" in data[2]:
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                        item.setForeground(QColor("#e9ecef"))

                    # تلوين الرصيد حسب القيمة
                    if col == 5 and data[2] not in ["الرصيد الافتتاحي", "الرصيد الختامي"]:
                        try:
                            balance_value = float(value.replace(',', ''))
                            if balance_value > 0:
                                item.setForeground(QColor("#e8f5e8"))
                            elif balance_value < 0:
                                item.setForeground(QColor("#ffe8e8"))
                        except:
                            pass

                    # تلوين المدين والدائن
                    if col == 3 and value != "0.00" and value != "":  # مدين
                        item.setForeground(QColor("#e8f5e8"))
                    elif col == 4 and value != "0.00" and value != "":  # دائن
                        item.setForeground(QColor("#e8f0ff"))

                    self.accounting_table.setItem(row, col, item)

            # تنسيق الجدول
            header = self.accounting_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # رقم القيد
            header.setSectionResizeMode(2, QHeaderView.Stretch)           # وصف الحركة
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # مدين
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # دائن
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الرصيد

            # تحديث معلومات التقرير
            self.report_title_label.setText(f"كشف حساب: {account_name}")
            self.report_date_label.setText(f"من {from_date} إلى {to_date}")
            self.report_total_label.setText(f"الرصيد الختامي: {running_balance:,.2f}")

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء كشف الحساب: {e}")
            raise e

    # ===== وظائف التقارير المحاسبية الإضافية =====

    def on_accounting_report_changed(self):
        """معالج تغيير نوع التقرير المحاسبي"""
        try:
            report_type = self.accounting_report_combo.currentText()

            # إخفاء جميع الخيارات أولاً
            self.account_combo.setVisible(False)
            self.detail_level_combo.setVisible(False)
            self.show_zero_balances.setVisible(False)

            # إظهار الخيارات المناسبة حسب نوع التقرير
            if report_type == "كشف حساب تفصيلي":
                self.account_combo.setVisible(True)
                self.load_accounts_for_statement()
            elif report_type == "ميزان المراجعة":
                self.detail_level_combo.setVisible(True)
                self.show_zero_balances.setVisible(True)

            # تحديث عنوان التقرير
            self.report_title_label.setText(f"اختر خيارات {report_type}")

        except Exception as e:
            print(f"خطأ في تغيير نوع التقرير: {e}")

    def load_accounts_for_statement(self):
        """تحميل الحسابات لكشف الحساب"""
        try:
            self.account_combo.clear()

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # جلب الحسابات النهائية فقط
            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, نوع_الحساب
                FROM شجرة_الحسابات
                WHERE حساب_نهائي = TRUE
                ORDER BY كود_الحساب
            """)

            accounts = cursor.fetchall()

            for account in accounts:
                display_text = f"{account['كود_الحساب']} - {account['اسم_الحساب']}"
                self.account_combo.addItem(display_text, account['كود_الحساب'])

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الحسابات: {e}")

    def export_accounting_excel(self):
        """تصدير التقرير المحاسبي إلى Excel"""
        try:
            if self.accounting_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"تقرير_محاسبي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                # هنا يمكن إضافة كود التصدير إلى Excel
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def export_accounting_pdf(self):
        """تصدير التقرير المحاسبي إلى PDF"""
        try:
            if self.accounting_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"تقرير_محاسبي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                # هنا يمكن إضافة كود التصدير إلى PDF
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def print_accounting_report(self):
        """طباعة التقرير المحاسبي"""
        try:
            if self.accounting_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة")
                return

            # هنا يمكن إضافة كود الطباعة
            QMessageBox.information(self, "قيد التطوير", "طباعة التقارير المحاسبية قيد التطوير وستكون متاحة قريباً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def refresh_accounting_data(self):
        """تحديث بيانات التقارير المحاسبية"""
        try:
            # إعادة إنشاء التقرير الحالي
            self.generate_accounting_report()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات:\n{str(e)}")

    def show_accounting_context_menu(self, position):
        """عرض قائمة السياق للتقارير المحاسبية"""
        try:
            item = self.accounting_table.itemAt(position)
            if not item:
                return

            context_menu = QMenu(self)

            # إضافة العمليات
            export_excel_action = context_menu.addAction("📄 تصدير Excel")
            export_excel_action.triggered.connect(self.export_accounting_excel)

            export_pdf_action = context_menu.addAction("📑 تصدير PDF")
            export_pdf_action.triggered.connect(self.export_accounting_pdf)

            context_menu.addSeparator()

            print_action = context_menu.addAction("🖨️ طباعة")
            print_action.triggered.connect(self.print_accounting_report)

            refresh_action = context_menu.addAction("🔄 تحديث")
            refresh_action.triggered.connect(self.refresh_accounting_data)

            # عرض القائمة
            context_menu.exec(self.accounting_table.mapToGlobal(position))

        except Exception as e:
            print(f"خطأ في عرض قائمة السياق: {e}")

    def update_item_combo(self):
        """تحديث قائمة العناصر حسب نوع التقرير"""
        try:
            report_type = self.detailed_report_combo.currentText()
            self.item_combo.clear()

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            if report_type == "كشف حساب عميل":
                cursor.execute("SELECT id, اسم_العميل FROM العملاء ORDER BY اسم_العميل")
                items = cursor.fetchall()
                for item in items:
                    self.item_combo.addItem(item['اسم_العميل'], item['id'])

            elif report_type == "كشف حساب موظف":
                cursor.execute("SELECT id, اسم_الموظف FROM الموظفين ORDER BY اسم_الموظف")
                items = cursor.fetchall()
                for item in items:
                    self.item_combo.addItem(item['اسم_الموظف'], item['id'])

            elif report_type == "تفاصيل مشروع":
                cursor.execute("SELECT id, اسم_المشروع FROM المشاريع ORDER BY اسم_المشروع")
                items = cursor.fetchall()
                for item in items:
                    self.item_combo.addItem(item['اسم_المشروع'], item['id'])

            elif report_type == "حركات حساب محاسبي":
                cursor.execute("SELECT كود_الحساب, اسم_الحساب FROM شجرة_الحسابات WHERE حساب_نهائي = TRUE ORDER BY كود_الحساب")
                items = cursor.fetchall()
                for item in items:
                    self.item_combo.addItem(f"{item['كود_الحساب']} - {item['اسم_الحساب']}", item['كود_الحساب'])

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث قائمة العناصر: {e}")

    def generate_detailed_report(self):
        """إنشاء التقرير التفصيلي"""
        try:
            # إظهار شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("جاري إنشاء التقرير التفصيلي...")

            # الحصول على المعاملات
            report_type = self.detailed_report_combo.currentText()
            from_date = self.detailed_from_date.date().toString("yyyy-MM-dd")
            to_date = self.detailed_to_date.date().toString("yyyy-MM-dd")

            # التحقق من اختيار العنصر
            if self.item_combo.currentData() is None and report_type not in ["تقرير المصروفات التفصيلي", "تقرير الدفعات التفصيلي"]:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للتقرير")
                self.progress_bar.setVisible(False)
                return

            self.progress_bar.setValue(20)

            # إنشاء التقرير حسب النوع
            if report_type == "كشف حساب عميل":
                self.generate_client_statement(from_date, to_date)
            elif report_type == "كشف حساب موظف":
                self.generate_employee_statement(from_date, to_date)
            elif report_type == "تفاصيل مشروع":
                self.generate_project_details(from_date, to_date)
            elif report_type == "حركات حساب محاسبي":
                self.generate_account_movements(from_date, to_date)
            elif report_type == "تقرير المصروفات التفصيلي":
                self.generate_expenses_detailed_report(from_date, to_date)
            elif report_type == "تقرير الدفعات التفصيلي":
                self.generate_payments_detailed_report(from_date, to_date)

            self.progress_bar.setValue(100)
            self.status_label.setText("تم إنشاء التقرير التفصيلي بنجاح")

            # إخفاء شريط التقدم بعد 3 ثوان
            QTimer.singleShot(3000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير التفصيلي:\n{str(e)}")
            print(f"خطأ في إنشاء التقرير التفصيلي: {e}")

    def generate_client_statement(self, from_date, to_date):
        """إنشاء كشف حساب عميل"""
        try:
            client_id = self.item_combo.currentData()
            if not client_id:
                return

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # الحصول على معلومات العميل
            cursor.execute("SELECT * FROM العملاء WHERE id = %s", (client_id,))
            client_info = cursor.fetchone()

            if not client_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات العميل")
                return

            # إعداد الجدول
            headers = ["التاريخ", "نوع المعاملة", "الوصف", "مدين", "دائن", "الرصيد", "المرجع"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            # جمع المعاملات من مصادر مختلفة
            transactions = []

            # المشاريع - استخدام معرف_العميل بدلاً من اسم_العميل للربط الصحيح
            cursor.execute("""
                SELECT تاريخ_الإستلام as التاريخ, 'مشروع' as النوع,
                       CONCAT('مشروع: ', اسم_المشروع) as الوصف,
                       0 as مدين, المبلغ as دائن, 'مشروع' as المرجع, id
                FROM المشاريع
                WHERE معرف_العميل = %s AND تاريخ_الإستلام BETWEEN %s AND %s
            """, (client_id, from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # الدفعات - استخدام معرف_العميل للربط الصحيح
            cursor.execute("""
                SELECT تاريخ_الدفع as التاريخ, 'دفعة' as النوع,
                       CONCAT('دفعة للمشروع: ', م.اسم_المشروع) as الوصف,
                       د.المبلغ_المدفوع as مدين, 0 as دائن, 'دفعة' as المرجع, د.id
                FROM المشاريع_المدفوعات د
                JOIN المشاريع م ON د.معرف_المشروع = م.id
                WHERE د.معرف_العميل = %s AND د.تاريخ_الدفع BETWEEN %s AND %s
            """, (client_id, from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # ترتيب المعاملات حسب التاريخ (مع معالجة القيم الفارغة)
            transactions.sort(key=lambda x: x['التاريخ'] if x['التاريخ'] else datetime.min.date())

            self.detailed_table.setRowCount(len(transactions) + 1)  # +1 للإجماليات

            running_balance = 0
            total_debit = 0
            total_credit = 0

            for row, transaction in enumerate(transactions):
                debit = transaction['مدين'] or 0
                credit = transaction['دائن'] or 0
                running_balance += credit - debit
                total_debit += debit
                total_credit += credit

                data = [
                    transaction['التاريخ'].strftime("%Y-%m-%d") if transaction['التاريخ'] else "",
                    transaction['النوع'],
                    transaction['الوصف'],
                    f"{debit:,.2f}" if debit > 0 else "",
                    f"{credit:,.2f}" if credit > 0 else "",
                    f"{running_balance:,.2f}",
                    transaction['المرجع']
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الخلايا
                    if col == 3 and debit > 0:  # مدين
                        item.setForeground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                    elif col == 4 and credit > 0:  # دائن
                        item.setForeground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif col == 5:  # الرصيد
                        if running_balance > 0:
                            item.setForeground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))
                        elif running_balance < 0:
                            item.setForeground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))

                    self.detailed_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = len(transactions)
            totals_data = [
                "", "", "الإجماليات",
                f"{total_debit:,.2f}",
                f"{total_credit:,.2f}",
                f"{running_balance:,.2f}",
                ""
            ]

            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#f5f5f5"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.detailed_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.detailed_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
            header.setSectionResizeMode(2, QHeaderView.Stretch)           # الوصف
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # مدين
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # دائن
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الرصيد
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # المرجع

            cursor.close()
            conn.close()

            self.progress_bar.setValue(60)

        except Exception as e:
            print(f"خطأ في إنشاء كشف حساب العميل: {e}")
            # إضافة معلومات تشخيصية أكثر
            if "Unknown column" in str(e):
                print("خطأ في اسم العمود - تحقق من بنية قاعدة البيانات")
            elif "doesn't exist" in str(e):
                print("خطأ في اسم الجدول - تحقق من وجود الجداول")
            raise e

    def generate_employee_statement(self, from_date, to_date):
        """إنشاء كشف حساب موظف"""
        try:
            employee_id = self.item_combo.currentData()
            if not employee_id:
                return

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # الحصول على معلومات الموظف
            cursor.execute("SELECT * FROM الموظفين WHERE id = %s", (employee_id,))
            employee_info = cursor.fetchone()

            if not employee_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات الموظف")
                return

            # إعداد الجدول
            headers = ["التاريخ", "نوع المعاملة", "الوصف", "مدين", "دائن", "الرصيد", "المرجع"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            # جمع المعاملات من مصادر مختلفة
            transactions = []

            # معاملات الموظف من جدول الحسابات (الرواتب والسلف)
            cursor.execute("""
                SELECT تاريخ_المصروف as التاريخ,
                       CASE
                           WHEN التصنيف LIKE '%راتب%' THEN 'راتب'
                           WHEN التصنيف LIKE '%سلفة%' THEN 'سلفة'
                           ELSE 'مصروف'
                       END as النوع,
                       CONCAT(التصنيف, ': ', المصروف) as الوصف,
                       CASE
                           WHEN التصنيف LIKE '%سلفة%' THEN المبلغ
                           ELSE 0
                       END as مدين,
                       CASE
                           WHEN التصنيف LIKE '%راتب%' THEN المبلغ
                           ELSE 0
                       END as دائن,
                       التصنيف as المرجع, id
                FROM الحسابات
                WHERE المستلم = %s AND تاريخ_المصروف BETWEEN %s AND %s
                   AND (التصنيف LIKE '%راتب%' OR التصنيف LIKE '%سلفة%')
            """, (employee_info['اسم_الموظف'], from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # معاملات إضافية من جدول مراحل المشروع (نسب المهندسين)
            cursor.execute("""
                SELECT تاريخ_البدء as التاريخ, 'نسبة مهندس' as النوع,
                       CONCAT('نسبة من مشروع: ', اسم_المشروع, ' - ', وصف_المرحلة) as الوصف,
                       0 as مدين, مبلغ_المهندس as دائن, 'نسبة' as المرجع, id
                FROM المشاريع_المراحل
                WHERE المهندس = %s AND تاريخ_البدء BETWEEN %s AND %s
                   AND مبلغ_المهندس > 0
            """, (employee_info['اسم_الموظف'], from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # ترتيب المعاملات حسب التاريخ (مع معالجة القيم الفارغة)
            transactions.sort(key=lambda x: x['التاريخ'] if x['التاريخ'] else datetime.min.date())

            self.detailed_table.setRowCount(len(transactions) + 1)  # +1 للإجماليات

            running_balance = 0
            total_debit = 0
            total_credit = 0

            for row, transaction in enumerate(transactions):
                debit = transaction['مدين'] or 0
                credit = transaction['دائن'] or 0
                running_balance += credit - debit
                total_debit += debit
                total_credit += credit

                data = [
                    transaction['التاريخ'].strftime("%Y-%m-%d") if transaction['التاريخ'] else "",
                    transaction['النوع'],
                    transaction['الوصف'],
                    f"{debit:,.2f}" if debit > 0 else "",
                    f"{credit:,.2f}" if credit > 0 else "",
                    f"{running_balance:,.2f}",
                    transaction['المرجع']
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الخلايا
                    if col == 3 and debit > 0:  # مدين
                        item.setForeground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                    elif col == 4 and credit > 0:  # دائن
                        item.setForeground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif col == 5:  # الرصيد
                        if running_balance > 0:
                            item.setForeground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))
                        elif running_balance < 0:
                            item.setForeground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))

                    self.detailed_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = len(transactions)
            totals_data = [
                "", "", "الإجماليات",
                f"{total_debit:,.2f}",
                f"{total_credit:,.2f}",
                f"{running_balance:,.2f}",
                ""
            ]

            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#f5f5f5"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.detailed_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.detailed_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

            self.progress_bar.setValue(70)

        except Exception as e:
            print(f"خطأ في إنشاء كشف حساب الموظف: {e}")
            # إضافة معلومات تشخيصية أكثر
            if "Unknown column" in str(e):
                print("خطأ في اسم العمود - تحقق من بنية قاعدة البيانات")
            elif "doesn't exist" in str(e):
                print("خطأ في اسم الجدول - تحقق من وجود الجداول")
            raise e

    def generate_project_details(self, from_date, to_date):
        """إنشاء تفاصيل مشروع"""
        try:
            project_id = self.item_combo.currentData()
            if not project_id:
                return

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # الحصول على معلومات المشروع
            cursor.execute("SELECT * FROM المشاريع WHERE id = %s", (project_id,))
            project_info = cursor.fetchone()

            if not project_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات المشروع")
                return

            # إعداد الجدول
            headers = ["التاريخ", "نوع المعاملة", "الوصف", "مدين", "دائن", "الرصيد", "المرجع"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            # جمع المعاملات من مصادر مختلفة
            transactions = []

            # إضافة المشروع نفسه
            if project_info['تاريخ_الإستلام'] and from_date <= project_info['تاريخ_الإستلام'].strftime("%Y-%m-%d") <= to_date:
                transactions.append({
                    'التاريخ': project_info['تاريخ_الإستلام'],
                    'النوع': 'مشروع',
                    'الوصف': f"إنشاء المشروع: {project_info['اسم_المشروع']}",
                    'مدين': 0,
                    'دائن': project_info['المبلغ'],
                    'المرجع': 'مشروع'
                })

            # الدفعات
            cursor.execute("""
                SELECT تاريخ_الدفع as التاريخ, 'دفعة' as النوع,
                       CONCAT('دفعة من العميل: ', طريقة_الدفع) as الوصف,
                       المبلغ_المدفوع as مدين, 0 as دائن, 'دفعة' as المرجع
                FROM المشاريع_المدفوعات
                WHERE معرف_المشروع = %s AND تاريخ_الدفع BETWEEN %s AND %s
            """, (project_id, from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # المصروفات المرتبطة بالمشروع
            cursor.execute("""
                SELECT تاريخ_المصروف as التاريخ, 'مصروف' as النوع,
                       CONCAT('مصروف: ', المصروف) as الوصف,
                       0 as مدين, المبلغ as دائن, 'مصروف' as المرجع
                FROM الحسابات
                WHERE ملاحظات LIKE %s AND تاريخ_المصروف BETWEEN %s AND %s
            """, (f"%مشروع_{project_id}%", from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # مراحل المشروع والمهندسين
            cursor.execute("""
                SELECT تاريخ_البدء as التاريخ, 'مرحلة' as النوع,
                       CONCAT('مرحلة: ', وصف_المرحلة, ' - مهندس: ', المهندس) as الوصف,
                       0 as مدين, مبلغ_المهندس as دائن, 'مرحلة' as المرجع
                FROM المشاريع_المراحل
                WHERE معرف_المشروع = %s AND تاريخ_البدء BETWEEN %s AND %s
                   AND مبلغ_المهندس > 0
            """, (project_id, from_date, to_date))

            for row in cursor.fetchall():
                transactions.append(row)

            # ترتيب المعاملات حسب التاريخ (مع معالجة القيم الفارغة)
            transactions.sort(key=lambda x: x['التاريخ'] if x['التاريخ'] else datetime.min.date())

            self.detailed_table.setRowCount(len(transactions) + 1)  # +1 للإجماليات

            running_balance = 0
            total_debit = 0
            total_credit = 0

            for row, transaction in enumerate(transactions):
                debit = transaction['مدين'] or 0
                credit = transaction['دائن'] or 0
                running_balance += credit - debit
                total_debit += debit
                total_credit += credit

                data = [
                    transaction['التاريخ'].strftime("%Y-%m-%d") if transaction['التاريخ'] else "",
                    transaction['النوع'],
                    transaction['الوصف'],
                    f"{debit:,.2f}" if debit > 0 else "",
                    f"{credit:,.2f}" if credit > 0 else "",
                    f"{running_balance:,.2f}",
                    transaction['المرجع']
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الخلايا
                    if col == 3 and debit > 0:  # مدين
                        item.setForeground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif col == 4 and credit > 0:  # دائن
                        item.setForeground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                    elif col == 5:  # الرصيد
                        if running_balance > 0:
                            item.setForeground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))
                        elif running_balance < 0:
                            item.setForeground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))

                    self.detailed_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = len(transactions)
            totals_data = [
                "", "", "الإجماليات",
                f"{total_debit:,.2f}",
                f"{total_credit:,.2f}",
                f"{running_balance:,.2f}",
                ""
            ]

            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#f5f5f5"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.detailed_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.detailed_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

            self.progress_bar.setValue(80)

        except Exception as e:
            print(f"خطأ في إنشاء تفاصيل المشروع: {e}")
            # إضافة معلومات تشخيصية أكثر
            if "Unknown column" in str(e):
                print("خطأ في اسم العمود - تحقق من بنية قاعدة البيانات")
            elif "doesn't exist" in str(e):
                print("خطأ في اسم الجدول - تحقق من وجود الجداول")
            raise e

    def export_detailed_report(self):
        """تصدير التقرير التفصيلي"""
        try:
            if self.detailed_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير التفصيلي",
                f"تقرير_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx);;PDF Files (*.pdf);;CSV Files (*.csv)"
            )

            if file_path:
                if file_path.endswith('.xlsx'):
                    self.export_detailed_to_excel(file_path)
                elif file_path.endswith('.pdf'):
                    self.export_detailed_to_pdf(file_path)
                elif file_path.endswith('.csv'):
                    self.export_detailed_to_csv(file_path)

                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def generate_account_movements(self, from_date, to_date):
        """إنشاء حركات حساب محاسبي"""
        try:
            account_code = self.item_combo.currentData()
            if not account_code:
                return

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # الحصول على معلومات الحساب
            cursor.execute("SELECT * FROM شجرة_الحسابات WHERE كود_الحساب = %s", (account_code,))
            account_info = cursor.fetchone()

            if not account_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات الحساب")
                return

            # إعداد الجدول
            headers = ["التاريخ", "رقم القيد", "الوصف", "مدين", "دائن", "الرصيد", "المرجع"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            # جلب حركات الحساب
            cursor.execute("""
                SELECT تاريخ_القيد as التاريخ, رقم_القيد, وصف_الحركة as الوصف,
                       مدين, دائن, المرجع
                FROM حركات_الحسابات
                WHERE كود_الحساب = %s AND تاريخ_القيد BETWEEN %s AND %s
                ORDER BY تاريخ_القيد, id
            """, (account_code, from_date, to_date))

            movements = cursor.fetchall()

            self.detailed_table.setRowCount(len(movements) + 1)  # +1 للإجماليات

            running_balance = account_info['الرصيد_الافتتاحي'] or 0
            total_debit = 0
            total_credit = 0

            for row, movement in enumerate(movements):
                debit = movement['مدين'] or 0
                credit = movement['دائن'] or 0

                # حساب الرصيد حسب طبيعة الحساب
                if account_info['طبيعة_الحساب'] == 'مدين':
                    running_balance += debit - credit
                else:
                    running_balance += credit - debit

                total_debit += debit
                total_credit += credit

                data = [
                    movement['التاريخ'].strftime("%Y-%m-%d") if movement['التاريخ'] else "",
                    movement['رقم_القيد'],
                    movement['الوصف'],
                    f"{debit:,.2f}" if debit > 0 else "",
                    f"{credit:,.2f}" if credit > 0 else "",
                    f"{running_balance:,.2f}",
                    movement['المرجع'] or ""
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين الخلايا
                    if col == 3 and debit > 0:  # مدين
                        item.setForeground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif col == 4 and credit > 0:  # دائن
                        item.setForeground(QColor("#e8f0ff"))
                        item.setForeground(QColor("#1565c0"))
                    elif col == 5:  # الرصيد
                        if running_balance > 0:
                            item.setForeground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))
                        elif running_balance < 0:
                            item.setForeground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))

                    self.detailed_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = len(movements)
            totals_data = [
                "", "", "الإجماليات",
                f"{total_debit:,.2f}",
                f"{total_credit:,.2f}",
                f"{running_balance:,.2f}",
                ""
            ]

            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#f5f5f5"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.detailed_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.detailed_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)

            cursor.close()
            conn.close()

            self.progress_bar.setValue(90)

        except Exception as e:
            print(f"خطأ في إنشاء حركات الحساب المحاسبي: {e}")
            raise e

    def generate_expenses_detailed_report(self, from_date, to_date):
        """إنشاء تقرير المصروفات التفصيلي"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["التاريخ", "التصنيف", "المصروف", "المبلغ", "المستلم", "رقم الفاتورة", "ملاحظات"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            # جلب المصروفات
            cursor.execute("""
                SELECT تاريخ_المصروف, التصنيف, المصروف, المبلغ, المستلم, رقم_الفاتورة, ملاحظات
                FROM الحسابات
                WHERE تاريخ_المصروف BETWEEN %s AND %s
                ORDER BY تاريخ_المصروف DESC, id DESC
            """, (from_date, to_date))

            expenses = cursor.fetchall()

            self.detailed_table.setRowCount(len(expenses) + 1)  # +1 للإجماليات

            total_amount = 0
            category_totals = {}

            for row, expense in enumerate(expenses):
                amount = expense['المبلغ'] or 0
                total_amount += amount

                # تجميع حسب التصنيف
                category = expense['التصنيف'] or "غير محدد"
                category_totals[category] = category_totals.get(category, 0) + amount

                data = [
                    expense['تاريخ_المصروف'].strftime("%Y-%m-%d") if expense['تاريخ_المصروف'] else "",
                    expense['التصنيف'] or "",
                    expense['المصروف'] or "",
                    f"{amount:,.2f}",
                    expense['المستلم'] or "",
                    expense['رقم_الفاتورة'] or "",
                    expense['ملاحظات'] or ""
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب المبلغ
                    if col == 3:  # عمود المبلغ
                        if amount > 1000:
                            item.setForeground(QColor("#ffebee"))
                            item.setForeground(QColor("#c62828"))
                        elif amount > 500:
                            item.setForeground(QColor("#fff3e0"))
                            item.setForeground(QColor("#ef6c00"))
                        else:
                            item.setForeground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))

                    self.detailed_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = len(expenses)
            totals_data = [
                "", "", "الإجمالي العام",
                f"{total_amount:,.2f}",
                "", "", f"عدد المصروفات: {len(expenses)}"
            ]

            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#f5f5f5"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.detailed_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.detailed_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(6, QHeaderView.Stretch)

            cursor.close()
            conn.close()

            self.progress_bar.setValue(95)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المصروفات التفصيلي: {e}")
            raise e

    def generate_payments_detailed_report(self, from_date, to_date):
        """إنشاء تقرير الدفعات التفصيلي"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول
            headers = ["التاريخ", "المشروع", "العميل", "المبلغ", "طريقة الدفع", "ملاحظات"]
            self.detailed_table.setColumnCount(len(headers))
            self.detailed_table.setHorizontalHeaderLabels(headers)

            # جلب الدفعات مع ربط صحيح للعميل
            cursor.execute("""
                SELECT د.تاريخ_الدفع, م.اسم_المشروع,
                       COALESCE(ع.اسم_العميل, م.اسم_العميل, 'غير محدد') as اسم_العميل,
                       د.المبلغ_المدفوع as المبلغ, د.طريقة_الدفع, د.وصف_المدفوع as ملاحظات
                FROM المشاريع_المدفوعات د
                JOIN المشاريع م ON د.معرف_المشروع = م.id
                LEFT JOIN العملاء ع ON م.معرف_العميل = ع.id
                WHERE د.تاريخ_الدفع BETWEEN %s AND %s
                ORDER BY د.تاريخ_الدفع DESC, د.id DESC
            """, (from_date, to_date))

            payments = cursor.fetchall()

            self.detailed_table.setRowCount(len(payments) + 1)  # +1 للإجماليات

            total_amount = 0
            payment_methods = {}

            for row, payment in enumerate(payments):
                amount = payment['المبلغ'] or 0
                total_amount += amount

                # تجميع حسب طريقة الدفع
                method = payment['طريقة_الدفع'] or "غير محدد"
                payment_methods[method] = payment_methods.get(method, 0) + amount

                data = [
                    payment['تاريخ_الدفع'].strftime("%Y-%m-%d") if payment['تاريخ_الدفع'] else "",
                    payment['اسم_المشروع'] or "",
                    payment['اسم_العميل'] or "",
                    f"{amount:,.2f}",
                    payment['طريقة_الدفع'] or "",
                    payment['ملاحظات'] or ""
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب المبلغ
                    if col == 3:  # عمود المبلغ
                        if amount > 5000:
                            item.setForeground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))
                        elif amount > 1000:
                            item.setForeground(QColor("#fff3e0"))
                            item.setForeground(QColor("#ef6c00"))
                        else:
                            item.setForeground(QColor("#e3f2fd"))
                            item.setForeground(QColor("#1565c0"))

                    self.detailed_table.setItem(row, col, item)

            # إضافة صف الإجماليات
            totals_row = len(payments)
            totals_data = [
                "", "", "الإجمالي العام",
                f"{total_amount:,.2f}",
                "", f"عدد الدفعات: {len(payments)}"
            ]

            for col, value in enumerate(totals_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#f5f5f5"))
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                self.detailed_table.setItem(totals_row, col, item)

            # تنسيق الجدول
            header = self.detailed_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.Stretch)
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(5, QHeaderView.Stretch)

            cursor.close()
            conn.close()

            self.progress_bar.setValue(100)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الدفعات التفصيلي: {e}")
            # إضافة معلومات تشخيصية أكثر
            if "Unknown column" in str(e):
                print("خطأ في اسم العمود - تحقق من بنية قاعدة البيانات")
                print("الأعمدة المطلوبة: تاريخ_الدفع, اسم_المشروع, اسم_العميل, المبلغ_المدفوع, طريقة_الدفع, وصف_المدفوع")
            elif "doesn't exist" in str(e):
                print("خطأ في اسم الجدول - تحقق من وجود الجداول")
                print("الجداول المطلوبة: المشاريع_المدفوعات, المشاريع, العملاء")
            raise e

    def export_detailed_to_excel(self, file_path):
        """تصدير التقرير التفصيلي إلى Excel"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # إنشاء مصنف جديد
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "التقرير التفصيلي"

            # إعداد الخط العربي
            arabic_font = Font(name='Arial Unicode MS', size=12)
            header_font = Font(name='Arial Unicode MS', size=14, bold=True)

            # إعداد التنسيق
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            center_alignment = Alignment(horizontal='center', vertical='center')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # كتابة العناوين
            for col in range(self.detailed_table.columnCount()):
                header_item = self.detailed_table.horizontalHeaderItem(col)
                if header_item:
                    cell = ws.cell(row=1, column=col+1, value=header_item.text())
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                    cell.border = border

            # كتابة البيانات
            for row in range(self.detailed_table.rowCount()):
                for col in range(self.detailed_table.columnCount()):
                    item = self.detailed_table.item(row, col)
                    if item:
                        cell = ws.cell(row=row+2, column=col+1, value=item.text())
                        cell.font = arabic_font
                        cell.alignment = center_alignment
                        cell.border = border

                        # نسخ التنسيق من الجدول
                        bg_color = item.background().color()
                        if bg_color.isValid():
                            hex_color = bg_color.name()[1:]  # إزالة #
                            cell.fill = PatternFill(start_color=hex_color, end_color=hex_color, fill_type="solid")

            # ضبط عرض الأعمدة
            for col in range(1, self.detailed_table.columnCount() + 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 20

            # حفظ الملف
            wb.save(file_path)

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel.")
        except Exception as e:
            raise e

    def export_detailed_to_pdf(self, file_path):
        """تصدير التقرير التفصيلي إلى PDF"""
        try:
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.lib import colors
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # تسجيل خط عربي
            try:
                pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
                arabic_font = 'Arabic'
            except:
                arabic_font = 'Helvetica'

            # إنشاء المستند
            doc = SimpleDocTemplate(file_path, pagesize=landscape(A4))
            story = []

            # إعداد الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'ArabicTitle',
                parent=styles['Title'],
                fontName=arabic_font,
                fontSize=16,
                alignment=1  # وسط
            )

            # العنوان
            report_type = self.detailed_report_combo.currentText()
            title = Paragraph(f"التقرير التفصيلي - {report_type}", title_style)
            story.append(title)
            story.append(Spacer(1, 20))

            # إعداد بيانات الجدول
            data = []

            # العناوين
            headers = []
            for col in range(self.detailed_table.columnCount()):
                header_item = self.detailed_table.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())
            data.append(headers)

            # البيانات
            for row in range(self.detailed_table.rowCount()):
                row_data = []
                for col in range(self.detailed_table.columnCount()):
                    item = self.detailed_table.item(row, col)
                    if item:
                        row_data.append(item.text())
                    else:
                        row_data.append("")
                data.append(row_data)

            # إنشاء الجدول
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRid', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)

            # بناء المستند
            doc.build(story)

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة reportlab غير مثبتة. يرجى تثبيتها لتصدير ملفات PDF.")
        except Exception as e:
            raise e

    def export_detailed_to_csv(self, file_path):
        """تصدير التقرير التفصيلي إلى CSV"""
        try:
            import csv

            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = []
                for col in range(self.detailed_table.columnCount()):
                    header_item = self.detailed_table.horizontalHeaderItem(col)
                    if header_item:
                        headers.append(header_item.text())
                writer.writerow(headers)

                # كتابة البيانات
                for row in range(self.detailed_table.rowCount()):
                    row_data = []
                    for col in range(self.detailed_table.columnCount()):
                        item = self.detailed_table.item(row, col)
                        if item:
                            row_data.append(item.text())
                        else:
                            row_data.append("")
                    writer.writerow(row_data)

        except Exception as e:
            raise e

    def load_journal_entries(self):
        """تحميل القيود المحاسبية"""
        try:
            from_date = self.journal_from_date.date().toString("yyyy-MM-dd")
            to_date = self.journal_to_date.date().toString("yyyy-MM-dd")
            entry_number = self.journal_number_edit.text().strip()
            entry_type = self.journal_type_combo.currentText()
            entry_status = self.journal_status_combo.currentText()

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # إعداد الجدول المحسن
            headers = ["رقم القيد", "التاريخ", "نوع القيد", "الوصف", "إجمالي مدين", "إجمالي دائن", "الحالة", "المستخدم"]
            self.journal_table.setColumnCount(len(headers))
            self.journal_table.setHorizontalHeaderLabels(headers)

            # بناء الاستعلام المحسن
            query = """
                SELECT
                    ق.رقم_القيد,
                    ق.تاريخ_القيد,
                    ق.نوع_القيد,
                    ق.وصف_القيد,
                    COALESCE(SUM(ح.مدين), 0) as إجمالي_مدين,
                    COALESCE(SUM(ح.دائن), 0) as إجمالي_دائن,
                    ق.حالة_القيد,
                    ق.المستخدم,
                    ق.تاريخ_الإنشاء
                FROM القيود_المحاسبية ق
                LEFT JOIN حركات_الحسابات ح ON ق.رقم_القيد = ح.رقم_القيد
                WHERE ق.تاريخ_القيد BETWEEN %s AND %s
            """
            params = [from_date, to_date]

            if entry_number:
                query += " AND ق.رقم_القيد LIKE %s"
                params.append(f"%{entry_number}%")

            if entry_type != "الكل":
                query += " AND ق.نوع_القيد = %s"
                params.append(entry_type)

            if entry_status != "الكل":
                query += " AND ق.حالة_القيد = %s"
                params.append(entry_status)

            query += " GROUP BY ق.رقم_القيد ORDER BY ق.تاريخ_القيد DESC, ق.رقم_القيد DESC"

            cursor.execute(query, params)
            entries = cursor.fetchall()

            self.journal_table.setRowCount(len(entries))

            total_debit = 0
            total_credit = 0

            for row, entry in enumerate(entries):
                debit = entry['إجمالي_مدين'] or 0
                credit = entry['إجمالي_دائن'] or 0
                total_debit += debit
                total_credit += credit

                data = [
                    entry['رقم_القيد'],
                    entry['تاريخ_القيد'].strftime("%Y-%m-%d") if entry['تاريخ_القيد'] else "",
                    entry['نوع_القيد'] or "عام",
                    entry['وصف_القيد'] or "",
                    f"{debit:,.2f}",
                    f"{credit:,.2f}",
                    entry['حالة_القيد'] or "مؤكد",
                    entry['المستخدم'] or ""
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تخزين رقم القيد في البيانات
                    if col == 0:
                        item.setData(Qt.UserRole, entry['رقم_القيد'])

                    # تلوين حسب الحالة
                    if col == 6:  # عمود الحالة
                        status = value
                        if "مؤكد" in status:
                            item.setForeground(QColor("#d4edda"))
                            item.setForeground(QColor("#155724"))
                        elif "مسودة" in status:
                            item.setForeground(QColor("#fff3cd"))
                            item.setForeground(QColor("#856404"))
                        elif "ملغي" in status:
                            item.setForeground(QColor("#f8d7da"))
                            item.setForeground(QColor("#721c24"))

                    self.journal_table.setItem(row, col, item)

            # تنسيق الجدول المحسن
            header = self.journal_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم القيد
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # نوع القيد
            header.setSectionResizeMode(3, QHeaderView.Stretch)           # الوصف
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # مدين
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # دائن
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
            header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # المستخدم

            # تحديث معلومات الإحصائيات
            self.journal_count_label.setText(f"عدد القيود: {len(entries)}")
            self.journal_total_debit_label.setText(f"إجمالي مدين: {total_debit:,.2f}")
            self.journal_total_credit_label.setText(f"إجمالي دائن: {total_credit:,.2f}")

            cursor.close()
            conn.close()

            self.status_label.setText(f"تم تحميل {len(entries)} قيد محاسبي")
            self.update_journal_buttons_state()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل القيود المحاسبية:\n{str(e)}")

    def update_journal_buttons_state(self):
        """تحديث حالة أزرار القيود حسب التحديد"""
        selected_rows = self.journal_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        self.edit_journal_btn.setEnabled(has_selection)
        self.view_journal_btn.setEnabled(has_selection)
        self.approve_journal_btn.setEnabled(has_selection)
        self.cancel_journal_btn.setEnabled(has_selection)
        self.delete_journal_btn.setEnabled(has_selection)
        self.print_journal_btn.setEnabled(has_selection)

        if has_selection:
            # فحص حالة القيد المحدد
            row = selected_rows[0].row()
            status_item = self.journal_table.item(row, 6)
            if status_item:
                status = status_item.text()

                # تحديث حالة الأزرار حسب حالة القيد
                if "مؤكد" in status:
                    self.approve_journal_btn.setEnabled(False)
                    self.edit_journal_btn.setEnabled(False)
                elif "ملغي" in status:
                    self.approve_journal_btn.setEnabled(False)
                    self.cancel_journal_btn.setEnabled(False)
                    self.edit_journal_btn.setEnabled(False)

    def on_journal_selection_changed(self):
        """معالج تغيير التحديد في جدول القيود"""
        self.update_journal_buttons_state()

    def create_new_journal_entry(self):
        """إنشاء قيد محاسبي جديد"""
        try:
            # محاولة استيراد نافذة إنشاء القيود
            try:
                from الأدوات import AddEntryDialog
                dialog = AddEntryDialog(self)
                if dialog.exec() == QDialog.Accepted:
                    self.load_journal_entries()
                    self.update_quick_info()
                    QMessageBox.information(self, "نجح", "تم إنشاء القيد المحاسبي بنجاح")
            except ImportError:
                QMessageBox.information(self, "معلومات", "نافذة إنشاء القيود غير متوفرة حالياً.\nيرجى التأكد من وجود ملف tools.py")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة القيد الجديد:\n{str(e)}")

    def view_journal_entry(self):
        """عرض تفاصيل القيد المحاسبي"""
        try:
            selected_rows = self.journal_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد لعرض تفاصيله")
                return

            row = selected_rows[0].row()
            entry_number = self.journal_table.item(row, 0).data(Qt.UserRole)

            # إنشاء نافذة عرض التفاصيل
            self.show_journal_details(entry_number)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض تفاصيل القيد:\n{str(e)}")

    def show_journal_details(self, entry_number):
        """عرض تفاصيل القيد في نافذة منفصلة"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # جلب تفاصيل القيد
            cursor.execute("""
                SELECT ق.*, ح.كود_الحساب, ش.اسم_الحساب, ح.وصف_الحركة, ح.مدين, ح.دائن
                FROM القيود_المحاسبية ق
                LEFT JOIN حركات_الحسابات ح ON ق.رقم_القيد = ح.رقم_القيد
                LEFT JOIN شجرة_الحسابات ش ON ح.كود_الحساب = ش.كود_الحساب
                WHERE ق.رقم_القيد = %s
                ORDER BY ح.id
            """, (entry_number,))

            details = cursor.fetchall()

            if not details:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على تفاصيل القيد")
                return

            # إنشاء نافذة التفاصيل
            details_dialog = QDialog(self)
            details_dialog.setWindowTitle(f"تفاصيل القيد رقم {entry_number}")
            details_dialog.setGeometry(100, 100, 800, 600)
            details_dialog.setLayoutDirection(Qt.RightToLeft)

            layout = QVBoxLayout(details_dialog)

            # معلومات القيد الأساسية
            info_frame = QFrame()
            info_frame.setObjectName("InfoFrame")
            info_layout = QFormLayout(info_frame)

            entry_info = details[0]
            info_layout.addRow("رقم القيد:", QLabel(str(entry_info['رقم_القيد'])))
            info_layout.addRow("تاريخ القيد:", QLabel(entry_info['تاريخ_القيد'].strftime("%Y-%m-%d") if entry_info['تاريخ_القيد'] else ""))
            info_layout.addRow("نوع القيد:", QLabel(entry_info['نوع_القيد'] or "عام"))
            info_layout.addRow("وصف القيد:", QLabel(entry_info['وصف_القيد'] or ""))
            info_layout.addRow("الحالة:", QLabel(entry_info['حالة_القيد'] or "مؤكد"))
            info_layout.addRow("المستخدم:", QLabel(entry_info['المستخدم'] or ""))

            layout.addWidget(info_frame)

            # جدول حركات القيد
            movements_table = QTableWidget()
            movements_table.setColumnCount(5)
            movements_table.setHorizontalHeaderLabels(["كود الحساب", "اسم الحساب", "وصف الحركة", "مدين", "دائن"])
            movements_table.setRowCount(len(details))

            total_debit = 0
            total_credit = 0

            for row, detail in enumerate(details):
                if detail['كود_الحساب']:  # تجاهل الصفوف الفارغة
                    debit = detail['مدين'] or 0
                    credit = detail['دائن'] or 0
                    total_debit += debit
                    total_credit += credit

                    movements_table.setItem(row, 0, QTableWidgetItem(detail['كود_الحساب']))
                    movements_table.setItem(row, 1, QTableWidgetItem(detail['اسم_الحساب'] or ""))
                    movements_table.setItem(row, 2, QTableWidgetItem(detail['وصف_الحركة'] or ""))
                    movements_table.setItem(row, 3, QTableWidgetItem(f"{debit:,.2f}"))
                    movements_table.setItem(row, 4, QTableWidgetItem(f"{credit:,.2f}"))

                    # تلوين الخلايا
                    for col in range(5):
                        item = movements_table.item(row, col)
                        if item:
                            item.setTextAlignment(Qt.AlignCenter)
                            if col == 3 and debit > 0:  # مدين
                                item.setForeground(QColor("#e8f5e8"))
                            elif col == 4 and credit > 0:  # دائن
                                item.setForeground(QColor("#e8f0ff"))

            # إضافة صف الإجماليات
            totals_row = movements_table.rowCount()
            movements_table.setRowCount(totals_row + 1)
            movements_table.setItem(totals_row, 0, QTableWidgetItem(""))
            movements_table.setItem(totals_row, 1, QTableWidgetItem(""))
            movements_table.setItem(totals_row, 2, QTableWidgetItem("الإجماليات"))
            movements_table.setItem(totals_row, 3, QTableWidgetItem(f"{total_debit:,.2f}"))
            movements_table.setItem(totals_row, 4, QTableWidgetItem(f"{total_credit:,.2f}"))

            # تنسيق صف الإجماليات
            for col in range(5):
                item = movements_table.item(totals_row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setForeground(QColor("#f8f9fa"))
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)

            # تنسيق الجدول
            header = movements_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.Stretch)
            header.setSectionResizeMode(2, QHeaderView.Stretch)
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

            layout.addWidget(movements_table)

            # أزرار الإغلاق
            buttons_layout = QHBoxLayout()
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(details_dialog.accept)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)
            layout.addLayout(buttons_layout)

            cursor.close()
            conn.close()

            details_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض تفاصيل القيد:\n{str(e)}")

    def edit_journal_entry(self):
        """تعديل قيد محاسبي"""
        try:
            selected_rows = self.journal_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للتعديل")
                return

            row = selected_rows[0].row()
            entry_number = self.journal_table.item(row, 0).data(Qt.UserRole)
            status = self.journal_table.item(row, 6).text()

            # فحص إمكانية التعديل
            if "مؤكد" in status:
                QMessageBox.warning(self, "تحذير", "لا يمكن تعديل قيد مؤكد")
                return
            elif "ملغي" in status:
                QMessageBox.warning(self, "تحذير", "لا يمكن تعديل قيد ملغي")
                return

            # فتح نافذة التعديل
            try:
                from الأدوات import AddEntryDialog
                dialog = AddEntryDialog(self, entry_number)
                if dialog.exec() == QDialog.Accepted:
                    self.load_journal_entries()
                    self.update_quick_info()
                    QMessageBox.information(self, "نجح", "تم تعديل القيد المحاسبي بنجاح")
            except ImportError:
                QMessageBox.information(self, "معلومات", "نافذة تعديل القيود غير متوفرة حالياً.\nيرجى التأكد من وجود ملف tools.py")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل القيد:\n{str(e)}")

    def approve_journal_entry(self):
        """اعتماد قيد محاسبي"""
        try:
            selected_rows = self.journal_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للاعتماد")
                return

            row = selected_rows[0].row()
            entry_number = self.journal_table.item(row, 0).data(Qt.UserRole)
            status = self.journal_table.item(row, 6).text()

            # فحص الحالة الحالية
            if "مؤكد" in status:
                QMessageBox.information(self, "معلومات", "القيد مؤكد بالفعل")
                return
            elif "ملغي" in status:
                QMessageBox.warning(self, "تحذير", "لا يمكن اعتماد قيد ملغي")
                return

            # تأكيد الاعتماد
            reply = QMessageBox.question(self, "تأكيد الاعتماد",
                                       f"هل أنت متأكد من اعتماد القيد رقم {entry_number}؟\n"
                                       "لن يمكن تعديله بعد الاعتماد.",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                conn = self.get_db_connection()
                if not conn:
                    return

                cursor = conn.cursor()

                # تحديث حالة القيد
                cursor.execute("""
                    UPDATE القيود_المحاسبية
                    SET حالة_القيد = 'مؤكد', تاريخ_الاعتماد = NOW(), معتمد_بواسطة = %s
                    WHERE رقم_القيد = %s
                """, (os.getenv('USERNAME', 'مستخدم'), entry_number))

                conn.commit()
                cursor.close()
                conn.close()

                # إعادة تحميل القيود
                self.load_journal_entries()
                QMessageBox.information(self, "نجح", f"تم اعتماد القيد رقم {entry_number} بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في اعتماد القيد:\n{str(e)}")

    def cancel_journal_entry(self):
        """إلغاء قيد محاسبي"""
        try:
            selected_rows = self.journal_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للإلغاء")
                return

            row = selected_rows[0].row()
            entry_number = self.journal_table.item(row, 0).data(Qt.UserRole)
            status = self.journal_table.item(row, 6).text()

            # فحص الحالة الحالية
            if "ملغي" in status:
                QMessageBox.information(self, "معلومات", "القيد ملغي بالفعل")
                return

            # تأكيد الإلغاء
            reply = QMessageBox.question(self, "تأكيد الإلغاء",
                                       f"هل أنت متأكد من إلغاء القيد رقم {entry_number}؟\n"
                                       "سيتم إلغاء جميع الحركات المرتبطة بهذا القيد.",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                conn = self.get_db_connection()
                if not conn:
                    return

                cursor = conn.cursor()

                # تحديث حالة القيد
                cursor.execute("""
                    UPDATE القيود_المحاسبية
                    SET حالة_القيد = 'ملغي', تاريخ_الإلغاء = NOW(), ملغي_بواسطة = %s
                    WHERE رقم_القيد = %s
                """, (os.getenv('USERNAME', 'مستخدم'), entry_number))

                # إلغاء الحركات المرتبطة (أو تحديث حالتها)
                cursor.execute("""
                    UPDATE حركات_الحسابات
                    SET حالة_الحركة = 'ملغية'
                    WHERE رقم_القيد = %s
                """, (entry_number,))

                conn.commit()
                cursor.close()
                conn.close()

                # إعادة تحميل القيود
                self.load_journal_entries()
                self.update_quick_info()
                QMessageBox.information(self, "نجح", f"تم إلغاء القيد رقم {entry_number} بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إلغاء القيد:\n{str(e)}")

    def delete_journal_entry(self):
        """حذف قيد محاسبي"""
        try:
            selected_rows = self.journal_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للحذف")
                return

            row = selected_rows[0].row()
            entry_number = self.journal_table.item(row, 0).data(Qt.UserRole)
            status = self.journal_table.item(row, 6).text()

            # فحص إمكانية الحذف
            if "مؤكد" in status:
                QMessageBox.warning(self, "تحذير", "لا يمكن حذف قيد مؤكد. يجب إلغاؤه أولاً.")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(self, "تأكيد الحذف",
                                       f"هل أنت متأكد من حذف القيد رقم {entry_number}؟\n"
                                       "سيتم حذف القيد وجميع حركاته نهائياً ولا يمكن التراجع عن هذا الإجراء.",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                conn = self.get_db_connection()
                if not conn:
                    return

                cursor = conn.cursor()

                try:
                    # حذف الحركات أولاً
                    cursor.execute("DELETE FROM حركات_الحسابات WHERE رقم_القيد = %s", (entry_number,))

                    # حذف القيد
                    cursor.execute("DELETE FROM القيود_المحاسبية WHERE رقم_القيد = %s", (entry_number,))

                    conn.commit()

                    # إعادة تحميل القيود
                    self.load_journal_entries()
                    self.update_quick_info()
                    QMessageBox.information(self, "نجح", f"تم حذف القيد رقم {entry_number} بنجاح")

                except Exception as e:
                    conn.rollback()
                    raise e
                finally:
                    cursor.close()
                    conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف القيد:\n{str(e)}")

    def print_journal_entry(self):
        """طباعة قيد محاسبي"""
        try:
            selected_rows = self.journal_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للطباعة")
                return

            row = selected_rows[0].row()
            entry_number = self.journal_table.item(row, 0).data(Qt.UserRole)

            # إنشاء تقرير طباعة للقيد
            self.generate_journal_print_report(entry_number)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة القيد:\n{str(e)}")

    def generate_journal_print_report(self, entry_number):
        """إنشاء تقرير طباعة للقيد"""
        QMessageBox.information(self, "قيد التطوير", "طباعة القيود قيد التطوير وستكون متاحة قريباً")

    def show_journal_context_menu(self, position):
        """عرض قائمة السياق للقيود"""
        try:
            item = self.journal_table.itemAt(position)
            if not item:
                return

            context_menu = QMenu(self)

            # إضافة العمليات
            view_action = context_menu.addAction("👁️ عرض تفاصيل")
            view_action.triggered.connect(self.view_journal_entry)

            edit_action = context_menu.addAction("✏️ تعديل")
            edit_action.triggered.connect(self.edit_journal_entry)

            context_menu.addSeparator()

            approve_action = context_menu.addAction("✅ اعتماد")
            approve_action.triggered.connect(self.approve_journal_entry)

            cancel_action = context_menu.addAction("❌ إلغاء")
            cancel_action.triggered.connect(self.cancel_journal_entry)

            context_menu.addSeparator()

            print_action = context_menu.addAction("🖨️ طباعة")
            print_action.triggered.connect(self.print_journal_entry)

            delete_action = context_menu.addAction("🗑️ حذف")
            delete_action.triggered.connect(self.delete_journal_entry)

            # تحديث حالة العمليات حسب حالة القيد
            row = item.row()
            status = self.journal_table.item(row, 6).text()

            if "مؤكد" in status:
                edit_action.setEnabled(False)
                approve_action.setEnabled(False)
                delete_action.setEnabled(False)
            elif "ملغي" in status:
                edit_action.setEnabled(False)
                approve_action.setEnabled(False)
                cancel_action.setEnabled(False)

            # عرض القائمة
            context_menu.exec(self.journal_table.mapToGlobal(position))

        except Exception as e:
            print(f"خطأ في عرض قائمة السياق: {e}")

    def load_account_tree(self):
        """تحميل شجرة الحسابات"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor(dictionary=True)

            # مسح الشجرة الحالية
            self.account_tree.clear()

            # جلب الحسابات من المستوى الأول
            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, نوع_الحساب, الرصيد_الحالي
                FROM شجرة_الحسابات
                WHERE المستوى = 1
                ORDER BY كود_الحساب
            """)
            level1_accounts = cursor.fetchall()

            # إنشاء عناصر المستوى الأول
            for account in level1_accounts:
                item = QTreeWidgetItem(self.account_tree)
                item.setText(0, account['اسم_الحساب'])
                item.setText(1, account['كود_الحساب'])
                item.setText(2, account['نوع_الحساب'])
                item.setText(3, f"{account['الرصيد_الحالي']:,.2f}")
                item.setData(0, Qt.UserRole, account['كود_الحساب'])

                # تحميل الحسابات الفرعية
                self.load_sub_accounts(cursor, item, account['كود_الحساب'])

            # توسيع الشجرة
            self.account_tree.expandAll()

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل شجرة الحسابات: {e}")

    def load_sub_accounts(self, cursor, parent_item, parent_code):
        """تحميل الحسابات الفرعية"""
        try:
            cursor.execute("""
                SELECT كود_الحساب, اسم_الحساب, نوع_الحساب, الرصيد_الحالي
                FROM شجرة_الحسابات
                WHERE الحساب_الأب = %s
                ORDER BY كود_الحساب
            """, (parent_code,))
            sub_accounts = cursor.fetchall()

            for account in sub_accounts:
                item = QTreeWidgetItem(parent_item)
                item.setText(0, account['اسم_الحساب'])
                item.setText(1, account['كود_الحساب'])
                item.setText(2, account['نوع_الحساب'])
                item.setText(3, f"{account['الرصيد_الحالي']:,.2f}")
                item.setData(0, Qt.UserRole, account['كود_الحساب'])

                # تحميل الحسابات الفرعية بشكل متكرر
                self.load_sub_accounts(cursor, item, account['كود_الحساب'])

        except Exception as e:
            print(f"خطأ في تحميل الحسابات الفرعية: {e}")

    def add_account(self):
        """إضافة حساب جديد"""
        try:
            try:
                from شجرة_الحسابات import AccountDialog
                dialog = AccountDialog(self)
                if dialog.exec() == QDialog.Accepted:
                    self.load_account_tree()
            except ImportError:
                QMessageBox.information(self, "معلومات", "نافذة إضافة الحسابات غير متوفرة حالياً.\nيرجى التأكد من وجود ملف شجرة_الحسابات.py")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إضافة الحساب:\n{str(e)}")

    def edit_account(self):
        """تعديل حساب"""
        QMessageBox.information(self, "قيد التطوير", "تعديل الحسابات قيد التطوير وستكون متاحة قريباً")

    def delete_account(self):
        """حذف حساب"""
        QMessageBox.information(self, "قيد التطوير", "حذف الحسابات قيد التطوير وستكون متاحة قريباً")

    def generate_analytics(self):
        """إنشاء التحليلات"""
        QMessageBox.information(self, "قيد التطوير", "التحليلات والرسوم البيانية قيد التطوير وستكون متاحة قريباً")

    def export_to_excel(self):
        """تصدير إلى Excel"""
        QMessageBox.information(self, "قيد التطوير", "تصدير Excel قيد التطوير وستكون متاحة قريباً")

    def export_accounting_report(self):
        """تصدير التقرير المحاسبي"""
        QMessageBox.information(self, "قيد التطوير", "تصدير التقارير المحاسبية قيد التطوير وستكون متاحة قريباً")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قيد التطوير", "طباعة التقارير قيد التطوير وستكون متاحة قريباً")


# دالة لفتح النظام المحاسبي الشامل
def open_unified_accounting_system(parent=None):
    """فتح النظام المحاسبي الشامل"""
    try:
        system = UnifiedAccountingSystem(parent)
        system.show()
        return system
    except Exception as e:
        print(f"خطأ في فتح النظام المحاسبي الشامل: {e}")
        if parent:
            QMessageBox.critical(parent, "خطأ", f"فشل في فتح النظام المحاسبي الشامل:\n{str(e)}")
        return None


# للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # اختبار النظام المحاسبي الشامل
    system = UnifiedAccountingSystem()
    system.show()

    sys.exit(app.exec_())
