#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام الربط المحاسبي التلقائي
يقوم بربط جميع المعاملات المالية في التطبيق بالنظام المحاسبي تلقائياً
"""

import mysql.connector
from datetime import datetime, date
from decimal import Decimal
import json

# استيراد إعدادات قاعدة البيانات
try:
    from قاعدة_البيانات import host, user, password
except ImportError:
    host = "localhost"
    user = "root"
    password = "kh123456"

class AccountingIntegration:
    """فئة إدارة الربط المحاسبي التلقائي"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        
        # أكواد الحسابات الافتراضية
        self.default_accounts = {
            # الأصول
            'صندوق': '1.1.1',
            'بنك': '1.1.2',
            'عملاء': '1.2.1',
            'موظفين': '1.2.2',
            
            # الخصوم
            'موردين': '2.1.1',
            'مستحقات_موظفين': '2.2.1',
            
            # الإيرادات
            'ايرادات_مشاريع': '3.1.1',
            'ايرادات_عقود': '3.1.2',
            'ايرادات_عقارات': '3.1.3',
            'ايرادات_تدريب': '3.1.4',
            
            # المصروفات
            'مصروفات_تشغيلية': '4.1.1',
            'رواتب': '4.1.2',
            'مصروفات_مشاريع': '4.1.3',
            'مصروفات_ادارية': '4.1.4'
        }
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connection = mysql.connector.connect(
                    host=host,
                    user=user,
                    password=password,
                    database="project_manager_V2",
                    charset='utf8mb4',
                    collation='utf8mb4_unicode_ci'
                )
                self.cursor = self.connection.cursor(dictionary=True)
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close_connection(self):
        """إغلاق اتصال قاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection and self.connection.is_connected():
                self.connection.close()
        except Exception as e:
            print(f"خطأ في إغلاق الاتصال: {e}")
    
    def generate_entry_number(self, entry_type="عام"):
        """توليد رقم قيد جديد"""
        try:
            year = datetime.now().year
            
            # الحصول على آخر رقم قيد للسنة الحالية
            self.cursor.execute("""
                SELECT MAX(CAST(SUBSTRING(رقم_القيد, LOCATE('-', رقم_القيد) + 1) AS UNSIGNED)) 
                FROM القيود_المحاسبية 
                WHERE السنة = %s AND رقم_القيد LIKE %s
            """, (year, f"{entry_type}-%"))
            
            result = self.cursor.fetchone()
            last_number = result['MAX(CAST(SUBSTRING(رقم_القيد, LOCATE(\'-\', رقم_القيد) + 1) AS UNSIGNED))'] if result else 0
            last_number = last_number if last_number else 0
            
            new_number = last_number + 1
            return f"{entry_type}-{new_number:06d}"
            
        except Exception as e:
            print(f"خطأ في توليد رقم القيد: {e}")
            return f"{entry_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def create_journal_entry(self, entry_data, details_list, auto_approve=True):
        """
        إنشاء قيد محاسبي جديد
        
        entry_data: بيانات القيد الرئيسية
        details_list: قائمة تفاصيل القيد
        auto_approve: اعتماد القيد تلقائياً
        """
        try:
            if not self.get_connection():
                return False, "فشل في الاتصال بقاعدة البيانات"
            
            # التحقق من توازن القيد
            total_debit = sum(detail.get('مدين', 0) for detail in details_list)
            total_credit = sum(detail.get('دائن', 0) for detail in details_list)
            
            if abs(total_debit - total_credit) > 0.01:  # السماح بفرق صغير للتقريب
                return False, f"القيد غير متوازن: مدين {total_debit}, دائن {total_credit}"
            
            # توليد رقم القيد
            entry_number = self.generate_entry_number(entry_data.get('نوع_القيد', 'عام'))
            
            # إعداد بيانات القيد
            entry_insert_data = (
                entry_number,
                entry_data.get('تاريخ_القيد', date.today()),
                entry_data.get('وصف_القيد', ''),
                total_debit,
                total_credit,
                'معتمد' if auto_approve else 'مسودة',
                entry_data.get('نوع_القيد', 'عام'),
                entry_data.get('المرجع_الخارجي', ''),
                entry_data.get('ملاحظات', ''),
                entry_data.get('المستخدم', 'النظام'),
                datetime.now().year
            )

            # إدراج القيد الرئيسي - التحقق من الأعمدة الموجودة أولاً
            try:
                self.cursor.execute("""
                    INSERT INTO القيود_المحاسبية
                    (رقم_القيد, تاريخ_القيد, وصف_القيد, إجمالي_مدين, إجمالي_دائن,
                     حالة_القيد, نوع_القيد, المرجع_الخارجي, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, entry_insert_data)
            except Exception as e:
                # إذا فشل، جرب بدون الأعمدة الإضافية
                entry_insert_data_simple = (
                    entry_number,
                    entry_data.get('تاريخ_القيد', date.today()),
                    entry_data.get('وصف_القيد', ''),
                    total_debit,
                    total_credit,
                    'معتمد' if auto_approve else 'مسودة'
                )

                self.cursor.execute("""
                    INSERT INTO القيود_المحاسبية
                    (رقم_القيد, تاريخ_القيد, وصف_القيد, إجمالي_مدين, إجمالي_دائن, حالة_القيد)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, entry_insert_data_simple)
            
            entry_id = self.cursor.lastrowid
            
            # إدراج تفاصيل القيد
            for i, detail in enumerate(details_list, 1):
                # الحصول على اسم الحساب
                account_name = self.get_account_name(detail['كود_الحساب'])
                
                detail_insert_data = (
                    entry_id,
                    entry_number,
                    detail['كود_الحساب'],
                    account_name,
                    detail.get('وصف_التفصيل', ''),
                    detail.get('مدين', 0),
                    detail.get('دائن', 0),
                    detail.get('ملاحظات', ''),
                    i
                )

                try:
                    self.cursor.execute("""
                        INSERT INTO تفاصيل_القيود_المحاسبية
                        (معرف_القيد, رقم_القيد, كود_الحساب, اسم_الحساب, وصف_التفصيل,
                         مدين, دائن, ملاحظات, ترتيب_السطر)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, detail_insert_data)
                except Exception as e:
                    # إذا فشل، جرب بدون الأعمدة الإضافية
                    detail_insert_data_simple = (
                        entry_id,
                        entry_number,
                        detail['كود_الحساب'],
                        account_name,
                        detail.get('وصف_التفصيل', ''),
                        detail.get('مدين', 0),
                        detail.get('دائن', 0)
                    )

                    self.cursor.execute("""
                        INSERT INTO تفاصيل_القيود_المحاسبية
                        (معرف_القيد, رقم_القيد, كود_الحساب, اسم_الحساب, وصف_التفصيل, مدين, دائن)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, detail_insert_data_simple)
                
                # إدراج حركة الحساب
                try:
                    self.cursor.execute("""
                        INSERT INTO حركات_الحسابات
                        (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن,
                         المرجع, نوع_المستند, رقم_المستند, ملاحظات, المستخدم, السنة)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        entry_number,
                        entry_data.get('تاريخ_القيد', date.today()),
                        detail['كود_الحساب'],
                        detail.get('وصف_التفصيل', ''),
                        detail.get('مدين', 0),
                        detail.get('دائن', 0),
                        entry_data.get('المرجع_الخارجي', ''),
                        entry_data.get('نوع_المستند', 'قيد محاسبي'),
                        entry_data.get('رقم_المستند', entry_number),
                        detail.get('ملاحظات', ''),
                        entry_data.get('المستخدم', 'النظام'),
                        datetime.now().year
                    ))
                except Exception as e:
                    # إذا فشل، جرب بدون الأعمدة الإضافية
                    try:
                        self.cursor.execute("""
                            INSERT INTO حركات_الحسابات
                            (رقم_القيد, تاريخ_القيد, كود_الحساب, وصف_الحركة, مدين, دائن)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (
                            entry_number,
                            entry_data.get('تاريخ_القيد', date.today()),
                            detail['كود_الحساب'],
                            detail.get('وصف_التفصيل', ''),
                            detail.get('مدين', 0),
                            detail.get('دائن', 0)
                        ))
                    except Exception as e2:
                        # إذا لم يوجد جدول حركات الحسابات، تجاهل
                        pass
            
            # تحديث أرصدة الحسابات
            if auto_approve:
                self.update_account_balances(details_list)
            
            self.connection.commit()
            return True, f"تم إنشاء القيد رقم {entry_number} بنجاح"
            
        except Exception as e:
            if self.connection:
                self.connection.rollback()
            print(f"خطأ في إنشاء القيد المحاسبي: {e}")
            return False, f"خطأ في إنشاء القيد: {str(e)}"
    
    def get_account_name(self, account_code):
        """الحصول على اسم الحساب من كوده"""
        try:
            self.cursor.execute("""
                SELECT اسم_الحساب FROM شجرة_الحسابات 
                WHERE كود_الحساب = %s
            """, (account_code,))
            
            result = self.cursor.fetchone()
            return result['اسم_الحساب'] if result else f"حساب {account_code}"
            
        except Exception as e:
            print(f"خطأ في الحصول على اسم الحساب: {e}")
            return f"حساب {account_code}"
    
    def update_account_balances(self, details_list):
        """تحديث أرصدة الحسابات"""
        try:
            for detail in details_list:
                account_code = detail['كود_الحساب']
                debit = detail.get('مدين', 0)
                credit = detail.get('دائن', 0)
                
                # تحديث الرصيد في شجرة الحسابات
                self.cursor.execute("""
                    UPDATE شجرة_الحسابات 
                    SET رصيد_مدين = رصيد_مدين + %s,
                        رصيد_دائن = رصيد_دائن + %s,
                        الرصيد_الحالي = (رصيد_مدين + %s) - (رصيد_دائن + %s)
                    WHERE كود_الحساب = %s
                """, (debit, credit, debit, credit, account_code))
                
        except Exception as e:
            print(f"خطأ في تحديث أرصدة الحسابات: {e}")
    
    def get_account_code(self, account_type):
        """الحصول على كود الحساب من النوع"""
        return self.default_accounts.get(account_type, '1.1.1')  # افتراضي: الصندوق

    # ===== وظائف الربط المحاسبي للمعاملات المختلفة =====

    def record_project_payment(self, payment_data):
        """تسجيل دفعة مشروع محاسبياً"""
        try:
            entry_data = {
                'تاريخ_القيد': payment_data.get('تاريخ_الدفع', date.today()),
                'وصف_القيد': f"دفعة مشروع - {payment_data.get('وصف_المدفوع', '')}",
                'نوع_القيد': 'دفعة_مشروع',
                'المرجع_الخارجي': f"مشروع_{payment_data.get('معرف_المشروع', '')}",
                'رقم_المستند': payment_data.get('id', ''),
                'المستخدم': payment_data.get('المستخدم', 'النظام')
            }

            amount = float(payment_data.get('المبلغ_المدفوع', 0))
            payment_method = payment_data.get('طريقة_الدفع', 'نقدي')

            # تحديد حساب الاستلام حسب طريقة الدفع
            if payment_method == 'نقدي':
                cash_account = self.get_account_code('صندوق')
            else:
                cash_account = self.get_account_code('بنك')

            revenue_account = self.get_account_code('ايرادات_مشاريع')

            details_list = [
                {
                    'كود_الحساب': cash_account,
                    'وصف_التفصيل': f"استلام دفعة مشروع - {payment_method}",
                    'مدين': amount,
                    'دائن': 0
                },
                {
                    'كود_الحساب': revenue_account,
                    'وصف_التفصيل': f"إيراد مشروع - {payment_data.get('وصف_المدفوع', '')}",
                    'مدين': 0,
                    'دائن': amount
                }
            ]

            success, message = self.create_journal_entry(entry_data, details_list)

            if success:
                # ربط القيد بالدفعة
                self.link_entry_to_transaction('المشاريع_المدفوعات', payment_data.get('id'), message.split()[-1])

            return success, message

        except Exception as e:
            print(f"خطأ في تسجيل دفعة المشروع: {e}")
            return False, f"خطأ في التسجيل المحاسبي: {str(e)}"

    def record_expense(self, expense_data):
        """تسجيل مصروف محاسبياً"""
        try:
            entry_data = {
                'تاريخ_القيد': expense_data.get('تاريخ_المصروف', date.today()),
                'وصف_القيد': f"مصروف - {expense_data.get('المصروف', '')}",
                'نوع_القيد': 'مصروف',
                'المرجع_الخارجي': f"مصروف_{expense_data.get('id', '')}",
                'رقم_المستند': expense_data.get('رقم_الفاتورة', ''),
                'المستخدم': expense_data.get('المستخدم', 'النظام')
            }

            amount = float(expense_data.get('المبلغ', 0))
            expense_type = expense_data.get('التصنيف', 'مصروفات_تشغيلية')

            # تحديد حساب المصروف
            if 'راتب' in expense_type or 'موظف' in expense_type:
                expense_account = self.get_account_code('رواتب')
            elif 'مشروع' in expense_type:
                expense_account = self.get_account_code('مصروفات_مشاريع')
            elif 'ادار' in expense_type:
                expense_account = self.get_account_code('مصروفات_ادارية')
            else:
                expense_account = self.get_account_code('مصروفات_تشغيلية')

            cash_account = self.get_account_code('صندوق')

            details_list = [
                {
                    'كود_الحساب': expense_account,
                    'وصف_التفصيل': f"مصروف {expense_type} - {expense_data.get('المصروف', '')}",
                    'مدين': amount,
                    'دائن': 0
                },
                {
                    'كود_الحساب': cash_account,
                    'وصف_التفصيل': f"دفع مصروف - {expense_data.get('المستلم', '')}",
                    'مدين': 0,
                    'دائن': amount
                }
            ]

            success, message = self.create_journal_entry(entry_data, details_list)

            if success:
                # ربط القيد بالمصروف
                self.link_entry_to_transaction('الحسابات', expense_data.get('id'), message.split()[-1])

            return success, message

        except Exception as e:
            print(f"خطأ في تسجيل المصروف: {e}")
            return False, f"خطأ في التسجيل المحاسبي: {str(e)}"

    def record_employee_transaction(self, employee_data):
        """تسجيل معاملة موظف محاسبياً"""
        try:
            entry_data = {
                'تاريخ_القيد': employee_data.get('التاريخ', date.today()),
                'وصف_القيد': f"معاملة موظف - {employee_data.get('نوع_المعاملة', '')}",
                'نوع_القيد': 'معاملة_موظف',
                'المرجع_الخارجي': f"موظف_{employee_data.get('معرف_الموظف', '')}",
                'المستخدم': employee_data.get('المستخدم', 'النظام')
            }

            amount = float(employee_data.get('المبلغ', 0))
            transaction_type = employee_data.get('نوع_المعاملة', '')

            employee_account = self.get_account_code('موظفين')
            cash_account = self.get_account_code('صندوق')
            salary_account = self.get_account_code('رواتب')

            if 'مرتب' in transaction_type or 'راتب' in transaction_type:
                # إضافة مرتب
                details_list = [
                    {
                        'كود_الحساب': salary_account,
                        'وصف_التفصيل': f"راتب موظف - {employee_data.get('اسم_الموظف', '')}",
                        'مدين': amount,
                        'دائن': 0
                    },
                    {
                        'كود_الحساب': employee_account,
                        'وصف_التفصيل': f"مستحق موظف - {employee_data.get('اسم_الموظف', '')}",
                        'مدين': 0,
                        'دائن': amount
                    }
                ]
            elif 'سحب' in transaction_type:
                # سحب موظف
                details_list = [
                    {
                        'كود_الحساب': employee_account,
                        'وصف_التفصيل': f"سحب موظف - {employee_data.get('اسم_الموظف', '')}",
                        'مدين': amount,
                        'دائن': 0
                    },
                    {
                        'كود_الحساب': cash_account,
                        'وصف_التفصيل': f"دفع سحب موظف - {employee_data.get('اسم_الموظف', '')}",
                        'مدين': 0,
                        'دائن': amount
                    }
                ]
            else:
                # معاملة عامة
                details_list = [
                    {
                        'كود_الحساب': employee_account,
                        'وصف_التفصيل': f"{transaction_type} - {employee_data.get('اسم_الموظف', '')}",
                        'مدين': amount if amount > 0 else 0,
                        'دائن': abs(amount) if amount < 0 else 0
                    },
                    {
                        'كود_الحساب': cash_account,
                        'وصف_التفصيل': f"نقدية معاملة موظف - {employee_data.get('اسم_الموظف', '')}",
                        'مدين': abs(amount) if amount < 0 else 0,
                        'دائن': amount if amount > 0 else 0
                    }
                ]

            success, message = self.create_journal_entry(entry_data, details_list)

            if success:
                # ربط القيد بمعاملة الموظف
                self.link_entry_to_transaction('الموظفين_معاملات_مالية', employee_data.get('id'), message.split()[-1])

            return success, message

        except Exception as e:
            print(f"خطأ في تسجيل معاملة الموظف: {e}")
            return False, f"خطأ في التسجيل المحاسبي: {str(e)}"

    def record_contract_payment(self, contract_payment_data):
        """تسجيل دفعة عقد محاسبياً"""
        try:
            entry_data = {
                'تاريخ_القيد': contract_payment_data.get('تاريخ_الدفعة', date.today()),
                'وصف_القيد': f"دفعة عقد - {contract_payment_data.get('وصف_الدفعة', '')}",
                'نوع_القيد': 'دفعة_عقد',
                'المرجع_الخارجي': f"عقد_{contract_payment_data.get('معرف_العقد', '')}",
                'رقم_المستند': contract_payment_data.get('id', ''),
                'المستخدم': contract_payment_data.get('المستخدم', 'النظام')
            }

            amount = float(contract_payment_data.get('المبلغ', 0))

            cash_account = self.get_account_code('صندوق')
            revenue_account = self.get_account_code('ايرادات_عقود')

            details_list = [
                {
                    'كود_الحساب': cash_account,
                    'وصف_التفصيل': f"استلام دفعة عقد - {contract_payment_data.get('وصف_الدفعة', '')}",
                    'مدين': amount,
                    'دائن': 0
                },
                {
                    'كود_الحساب': revenue_account,
                    'وصف_التفصيل': f"إيراد عقد - {contract_payment_data.get('وصف_الدفعة', '')}",
                    'مدين': 0,
                    'دائن': amount
                }
            ]

            success, message = self.create_journal_entry(entry_data, details_list)

            if success:
                # ربط القيد بدفعة العقد
                self.link_entry_to_transaction('دفعات_العقود', contract_payment_data.get('id'), message.split()[-1])

            return success, message

        except Exception as e:
            print(f"خطأ في تسجيل دفعة العقد: {e}")
            return False, f"خطأ في التسجيل المحاسبي: {str(e)}"

    def record_property_payment(self, property_payment_data):
        """تسجيل دفعة عقار محاسبياً"""
        try:
            entry_data = {
                'تاريخ_القيد': property_payment_data.get('تاريخ_الدفع', date.today()),
                'وصف_القيد': f"دفعة عقار - {property_payment_data.get('وصف_الدفعة', 'دفعة عقار')}",
                'نوع_القيد': 'دفعة_عقار',
                'المرجع_الخارجي': f"عقار_{property_payment_data.get('معرف_العقار', '')}",
                'رقم_المستند': property_payment_data.get('id', ''),
                'المستخدم': property_payment_data.get('المستخدم', 'النظام')
            }

            amount = float(property_payment_data.get('المبلغ_المدفوع', 0))
            payment_method = property_payment_data.get('طريقة_الدفع', 'نقدي')

            # تحديد حساب الاستلام حسب طريقة الدفع
            if payment_method == 'نقدي':
                cash_account = self.get_account_code('صندوق')
            else:
                cash_account = self.get_account_code('بنك')

            revenue_account = self.get_account_code('ايرادات_عقارات')

            details_list = [
                {
                    'كود_الحساب': cash_account,
                    'وصف_التفصيل': f"استلام دفعة عقار - {payment_method}",
                    'مدين': amount,
                    'دائن': 0
                },
                {
                    'كود_الحساب': revenue_account,
                    'وصف_التفصيل': f"إيراد عقار - {property_payment_data.get('المستلم', '')}",
                    'مدين': 0,
                    'دائن': amount
                }
            ]

            success, message = self.create_journal_entry(entry_data, details_list)

            if success:
                # ربط القيد بدفعة العقار
                self.link_entry_to_transaction('العقار_المدفوعات', property_payment_data.get('id'), message.split()[-1])

            return success, message

        except Exception as e:
            print(f"خطأ في تسجيل دفعة العقار: {e}")
            return False, f"خطأ في التسجيل المحاسبي: {str(e)}"

    def record_custody_transaction(self, custody_data):
        """تسجيل معاملة عهدة مالية محاسبياً"""
        try:
            entry_data = {
                'تاريخ_القيد': custody_data.get('تاريخ_الإنشاء', date.today()),
                'وصف_القيد': f"عهدة مالية - {custody_data.get('اسم_المشروع', '')}",
                'نوع_القيد': 'عهدة_مالية',
                'المرجع_الخارجي': f"عهدة_{custody_data.get('رقم_العهدة', '')}",
                'رقم_المستند': custody_data.get('رقم_العهدة', ''),
                'المستخدم': custody_data.get('المستخدم', 'النظام')
            }

            custody_amount = float(custody_data.get('مبلغ_العهدة', 0))
            office_percentage = float(custody_data.get('نسبة_المكتب', 0))
            office_amount = custody_amount * office_percentage / 100
            net_amount = custody_amount - office_amount

            cash_account = self.get_account_code('صندوق')
            project_expense_account = self.get_account_code('مصروفات_مشاريع')
            revenue_account = self.get_account_code('ايرادات_مشاريع')

            details_list = [
                {
                    'كود_الحساب': project_expense_account,
                    'وصف_التفصيل': f"عهدة مشروع - {custody_data.get('اسم_المشروع', '')}",
                    'مدين': net_amount,
                    'دائن': 0
                },
                {
                    'كود_الحساب': revenue_account,
                    'وصف_التفصيل': f"نسبة المكتب من العهدة - {custody_data.get('اسم_المشروع', '')}",
                    'مدين': 0,
                    'دائن': office_amount
                },
                {
                    'كود_الحساب': cash_account,
                    'وصف_التفصيل': f"صرف عهدة مالية - {custody_data.get('اسم_المشروع', '')}",
                    'مدين': 0,
                    'دائن': net_amount
                }
            ]

            success, message = self.create_journal_entry(entry_data, details_list)

            if success:
                # ربط القيد بالعهدة المالية
                self.link_entry_to_transaction('المقاولات_العهد', custody_data.get('id'), message.split()[-1])

            return success, message

        except Exception as e:
            print(f"خطأ في تسجيل العهدة المالية: {e}")
            return False, f"خطأ في التسجيل المحاسبي: {str(e)}"

    def link_entry_to_transaction(self, table_name, transaction_id, entry_number):
        """ربط القيد المحاسبي بالمعاملة الأصلية"""
        try:
            # إضافة عمود رقم القيد إلى الجدول إذا لم يكن موجوداً
            self.cursor.execute(f"""
                ALTER TABLE {table_name}
                ADD COLUMN IF NOT EXISTS رقم_القيد_المحاسبي VARCHAR(50)
            """)

            # تحديث المعاملة بربطها بالقيد
            self.cursor.execute(f"""
                UPDATE {table_name}
                SET رقم_القيد_المحاسبي = %s
                WHERE id = %s
            """, (entry_number, transaction_id))

            self.connection.commit()

        except Exception as e:
            print(f"خطأ في ربط القيد بالمعاملة: {e}")

    def get_transaction_entries(self, table_name, transaction_id):
        """الحصول على القيود المرتبطة بمعاملة معينة"""
        try:
            self.cursor.execute(f"""
                SELECT رقم_القيد_المحاسبي FROM {table_name}
                WHERE id = %s
            """, (transaction_id,))

            result = self.cursor.fetchone()
            if result and result['رقم_القيد_المحاسبي']:
                # جلب تفاصيل القيد
                self.cursor.execute("""
                    SELECT q.*, t.كود_الحساب, t.اسم_الحساب, t.مدين, t.دائن, t.وصف_التفصيل
                    FROM القيود_المحاسبية q
                    LEFT JOIN تفاصيل_القيود_المحاسبية t ON q.رقم_القيد = t.رقم_القيد
                    WHERE q.رقم_القيد = %s
                    ORDER BY t.ترتيب_السطر
                """, (result['رقم_القيد_المحاسبي'],))

                return self.cursor.fetchall()

            return []

        except Exception as e:
            print(f"خطأ في جلب القيود المرتبطة: {e}")
            return []
