from الإعدادات_العامة import*
from متغيرات import*
from decimal import Decimal, InvalidOperation

#اضافة دفعة من البطاقة =================================================================================
def proj_Add_paid_from_card(parent_window, project_data, update_callback=None):
    """دالة إضافة دفعة جديدة من واجهة بطاقات المشاريع"""

    # التحقق من وجود البيانات المطلوبة
    if not project_data or not project_data.get('id'):
        QMessageBox.warning(parent_window, "خطأ", "بيانات المشروع غير مكتملة")
        return

    # استخراج البيانات من المشروع
    معرف_المشروع = project_data.get('id')
    معرف_العميل = project_data.get('معرف_العميل')
    اسم_المشروع = project_data.get('اسم_المشروع', '')
    اسم_العميل = project_data.get('اسم_العميل', '')
    المبلغ_الكلي = project_data.get('المبلغ', 0)
    الباقي = project_data.get('الباقي', 0)

    # إنشاء نافذة إدخال الدفعة
    dialog = QDialog(parent_window)
    dialog.setWindowTitle("إضافة دفعة جديدة")
    dialog.resize(600, 450)
    dialog.setLayoutDirection(Qt.RightToLeft)
    dialog.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
    dialog.setModal(True)

    layout = QVBoxLayout(dialog)

    # عنوان النافذة
    title_label = QLabel("إضافة دفعة جديدة")
    title_label.setAlignment(Qt.AlignCenter)
    title_label.setStyleSheet("""
        QLabel {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            margin-bottom: 10px;
        }
    """)
    layout.addWidget(title_label)

    # معلومات المشروع (للعرض فقط)
    info_frame = QFrame()
    info_frame.setFrameStyle(QFrame.Box)
    info_frame.setStyleSheet("""
        QFrame {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
        }
    """)
    info_layout = QGridLayout(info_frame)

    # معلومات المشروع
    info_layout.addWidget(QLabel("اسم العميل:"), 0, 0)
    client_info = QLabel(str(اسم_العميل))
    client_info.setStyleSheet("font-weight: bold; color: #495057;")
    info_layout.addWidget(client_info, 0, 1)

    info_layout.addWidget(QLabel("اسم المشروع:"), 0, 2)
    project_info = QLabel(str(اسم_المشروع))
    project_info.setStyleSheet("font-weight: bold; color: #495057;")
    info_layout.addWidget(project_info, 0, 3)

    info_layout.addWidget(QLabel("المبلغ الإجمالي:"), 1, 0)
    total_info = QLabel(f"{المبلغ_الكلي}")
    total_info.setStyleSheet("font-weight: bold; color: #28a745;")
    info_layout.addWidget(total_info, 1, 1)

    info_layout.addWidget(QLabel("المبلغ المتبقي:"), 1, 2)
    remaining_info = QLabel(f"{الباقي}")
    remaining_info.setStyleSheet("font-weight: bold; color: #dc3545;")
    info_layout.addWidget(remaining_info, 1, 3)

    layout.addWidget(info_frame)

    # نموذج إدخال بيانات الدفعة
    form_frame = QFrame()
    form_frame.setStyleSheet("""
        QFrame {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
    """)
    form_layout = QGridLayout(form_frame)

    # حقل مبلغ الدفعة
    form_layout.addWidget(QLabel("مبلغ الدفعة *:"), 0, 0)
    amount_edit = QLineEdit()
    amount_edit.setPlaceholderText("أدخل مبلغ الدفعة")
    amount_edit.setAlignment(Qt.AlignCenter)
    amount_edit.setText(str(الباقي))  # تعبئة تلقائية بالمبلغ المتبقي
    form_layout.addWidget(amount_edit, 0, 1)

    # حقل تاريخ الدفعة
    form_layout.addWidget(QLabel("تاريخ الدفعة *:"), 1, 0)
    date_edit = QDateEdit()
    date_edit.setDate(QDate.currentDate())
    date_edit.setCalendarPopup(True)
    date_edit.setDisplayFormat("dd/MM/yyyy")
    date_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(date_edit, 1, 1)

    # حقل طريقة الدفع
    form_layout.addWidget(QLabel("طريقة الدفع:"), 2, 0)
    payment_method = QComboBox()
    payment_method.addItems(["دفع نقدًا", "تحويل بنكي", "شيك"])
    payment_method.setCurrentText("دفع نقدًا")
    delegate = AlignedItemDelegate(payment_method)
    payment_method.setItemDelegate(delegate)
    line_edit = QtWidgets.QLineEdit()
    line_edit.setAlignment(QtCore.Qt.AlignCenter)
    payment_method.setLineEdit(line_edit)
    payment_method.setEditable(True)
    payment_method.lineEdit().setReadOnly(True)
    form_layout.addWidget(payment_method, 2, 1)

    # حقل وصف الدفعة
    form_layout.addWidget(QLabel("وصف الدفعة:"), 3, 0)
    description_edit = QLineEdit()
    description_edit.setPlaceholderText("وصف أو ملاحظات الدفعة (اختياري)")
    description_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(description_edit, 3, 1)

    # حقل المستلم
    form_layout.addWidget(QLabel("المستلم:"), 4, 0)
    receiver_edit = QLineEdit()
    receiver_edit.setPlaceholderText("اسم مستلم الدفعة (اختياري)")
    receiver_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(receiver_edit, 4, 1)

    layout.addWidget(form_frame)

    # أزرار الإجراءات
    buttons_layout = QHBoxLayout()

    # زر الحفظ
    save_button = QPushButton(qta.icon('fa5s.plus', color='white'), "إضافة الدفعة")
    save_button.setStyleSheet("""
        QPushButton {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #218838;
        }
        QPushButton:pressed {
            background-color: #1e7e34;
        }
    """)

    # زر الإلغاء
    cancel_button = QPushButton(qta.icon('fa5s.times', color='white'), "إلغاء")
    cancel_button.setStyleSheet("""
        QPushButton {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5a6268;
        }
        QPushButton:pressed {
            background-color: #545b62;
        }
    """)

    buttons_layout.addStretch()
    buttons_layout.addWidget(save_button)
    buttons_layout.addWidget(cancel_button)
    layout.addLayout(buttons_layout)

    # دالة حفظ الدفعة
    def save_payment():
        """حفظ الدفعة الجديدة"""
        try:
            # التحقق من صحة البيانات
            payment_amount_text = amount_edit.text().strip()
            if not payment_amount_text:
                QMessageBox.warning(dialog, 'خطأ', 'يرجى إدخال مبلغ الدفعة')
                amount_edit.setFocus()
                return

            try:
                payment_amount = Decimal(payment_amount_text)
                if payment_amount <= 0:
                    QMessageBox.warning(dialog, 'خطأ', 'مبلغ الدفعة يجب أن يكون أكبر من صفر')
                    amount_edit.setFocus()
                    return
            except (ValueError, InvalidOperation):
                QMessageBox.warning(dialog, 'خطأ', 'مبلغ الدفعة يجب أن يكون رقماً صحيحاً')
                amount_edit.setFocus()
                return

            # جمع بيانات الدفعة
            payment_date = date_edit.date().toString("yyyy-MM-dd")
            description = description_edit.text().strip()
            payment_method_text = payment_method.currentText()
            receiver = receiver_edit.text().strip()

            # التحقق من تجاوز المبلغ الإجمالي
            current_paid = Decimal(str(المبلغ_الكلي)) - Decimal(str(الباقي))
            total_after_payment = current_paid + payment_amount

            if total_after_payment > Decimal(str(المبلغ_الكلي)):
                reply = QMessageBox.question(
                    dialog,
                    'تأكيد إضافة دفعة',
                    f'المبلغ المدفوع سيتجاوز المبلغ الإجمالي بمقدار {total_after_payment - Decimal(str(المبلغ_الكلي))}\nهل تريد الاستمرار؟',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return

            # تأكيد الحفظ
            reply = QMessageBox.question(
                dialog,
                'تأكيد إضافة دفعة',
                f'هل تريد إضافة دفعة بمبلغ {payment_amount}؟',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply != QMessageBox.Yes:
                return

            # الاتصال بقاعدة البيانات
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            # إدراج الدفعة في قاعدة البيانات
            cursor.execute("""
                INSERT INTO المشاريع_المدفوعات
                (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (معرف_العميل, معرف_المشروع, float(payment_amount), description, payment_date, payment_method_text, 0, receiver, 'النظام'))

            # الحصول على id الدفعة المضافة
            payment_id = cursor.lastrowid

            # تحديث حالة المشروع إذا لزم الأمر
            cursor.execute("SELECT الوقت_المتبقي, الباقي, الحالة FROM المشاريع WHERE id = %s", (معرف_المشروع,))
            result = cursor.fetchone()
            if result:
                remaining_time, remaining_amount, current_status = result

                # تحديث الحالة فقط إذا كانت "قيد الإنجاز" أو "غير خالص"
                if current_status in ["قيد الإنجاز", "غير خالص"]:
                    new_status = (
                        "تأكيد التسليم" if remaining_amount == 0 and "تم الإنجاز" in str(remaining_time) else
                        "قيد الإنجاز" if "تم الإنجاز" not in str(remaining_time) else
                        "غير خالص"
                    )
                    if current_status != new_status:
                        cursor.execute("UPDATE المشاريع SET الحالة = %s WHERE id = %s", (new_status, معرف_المشروع))

            conn.commit()

            # الربط المحاسبي
            try:
                from تكامل_المحاسبة import AccountingIntegration
                accounting = AccountingIntegration()

                # الحصول على اسم العميل واسم المشروع للنظام المحاسبي
                try:
                    cursor.execute("""
                        SELECT c.اسم_العميل, p.اسم_المشروع
                        FROM العملاء c
                        JOIN المشاريع p ON p.معرف_العميل = c.id
                        WHERE p.id = %s
                    """, (معرف_المشروع,))
                    result = cursor.fetchone()
                    client_name = result[0] if result else "غير محدد"
                    project_name = result[1] if result else "غير محدد"
                except Exception as e:
                    print(f"خطأ في جلب أسماء العميل والمشروع: {e}")
                    client_name = "غير محدد"
                    project_name = "غير محدد"

                payment_data = {
                    'id': payment_id,
                    'معرف_المشروع': معرف_المشروع,
                    'معرف_العميل': معرف_العميل,
                    'وصف_المدفوع': description,
                    'المبلغ_المدفوع': float(payment_amount),
                    'تاريخ_الدفع': payment_date,
                    'طريقة_الدفع': payment_method_text,
                    'المستلم': receiver,
                    'المستخدم': 'النظام',
                    'اسم_العميل': client_name,  # إضافة اسم العميل للنظام المحاسبي
                    'اسم_المشروع': project_name  # إضافة اسم المشروع للنظام المحاسبي
                }

                success, message = accounting.record_project_payment(payment_data)
                if success:
                    print(f"تم تسجيل الدفعة محاسبياً: {message}")
                else:
                    print(f"خطأ في التسجيل المحاسبي: {message}")

                accounting.close_connection()

            except Exception as e:
                print(f"خطأ في الربط المحاسبي: {e}")

            conn.close()

            # عرض رسالة النجاح
            QMessageBox.information(dialog, 'نجاح', f'تم إضافة الدفعة رقم {payment_id} بنجاح')

            # تحديث البطاقة إذا تم تمرير دالة التحديث
            if update_callback:
                update_callback()

            # إغلاق النافذة
            dialog.accept()

        except Exception as e:
            QMessageBox.critical(dialog, 'خطأ', f'حدث خطأ أثناء حفظ الدفعة:\n{str(e)}')
            print(f"خطأ في حفظ الدفعة: {e}")

    # ربط الأزرار
    save_button.clicked.connect(save_payment)
    cancel_button.clicked.connect(dialog.reject)

    # تطبيق التنقل بين الحقول
    focus_widgets = [amount_edit, date_edit, payment_method, description_edit, receiver_edit]
    apply_enter_focus(dialog, "إضافة الدفعة", focus_widgets)

    # تركيز على حقل المبلغ
    amount_edit.setFocus()
    amount_edit.selectAll()

    # عرض النافذة
    dialog.exec()

#اضافة دفعة =================================================================================
def proj_Add_paid(self,table,selected_row_data,year,section):
    selected_items = selected_row_data
    if not selected_items:
        QMessageBox.warning(self, "خطأ", "يجب تحديد معاملة أولا")
        return  
    # استخراج البيانات من الصف المحدد

    self.selected_row = table.currentRow()
    معرف_المشروع = get_column_index_by_key(table, "المشاريع", "id")  
    self.معرف_المشروع = table.item(self.selected_row, معرف_المشروع).text()

    معرف_العميل = get_column_index_by_key(table, "المشاريع", "معرف_العميل")
    self.معرف_العميل = table.item(self.selected_row, معرف_العميل).text()

    اسم_المشروع =get_column_index_by_key(table, "المشاريع", "اسم_المشروع")
    self.اسم_المشروع = table.item(self.selected_row, اسم_المشروع).text()

    # الحصول على اسم العميل من قاعدة البيانات مباشرة
    try:
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT c.اسم_العميل FROM العملاء c WHERE c.id = %s", (self.معرف_العميل,))
        result = cursor.fetchone()
        self.name_العميل = result[0] if result else "غير محدد"
        conn.close()
    except Exception as e:
        print(f"خطأ في جلب اسم العميل: {e}")
        self.name_العميل = "غير محدد"

    المبلغ = get_column_index_by_key(table, "المشاريع", "المبلغ")
    self.المبلغ = table.item(self.selected_row, المبلغ).text()

    الباقي = get_column_index_by_key(table, "المشاريع", "الباقي")
    self.الباقي = table.item(self.selected_row, الباقي).text()
    

    # فتح نافذة الدفعات
    dialog = QDialog(self)
    dialog.setWindowTitle("دفعات العميل")
    dialog.resize(1000, 650)
    dialog.setLayoutDirection(Qt.RightToLeft)
    dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

    layout = QVBoxLayout(dialog)
    # استخدام QGridLayout لترتيب العناصر
    grمعرف_layout = QGridLayout()
        
       # إنشاء الحقول للإدخال
    #form_layout = QtWidgets.QGridLayout()
    self.code_edit = QLineEdit(dialog)
    self.name_edit = QLineEdit(dialog)
    
    self.project_edit = QLineEdit(dialog)
    self.Amount_edit = QLineEdit(dialog)
    self.date_edit = QDateEdit(dialog)
    self.description_edit = QLineEdit(dialog)
    self.amount_edit = QLineEdit(dialog)

    self.tarect_aldafa = QComboBox(dialog)
    delegate = AlignedItemDelegate(self.tarect_aldafa)
    self.tarect_aldafa.setItemDelegate(delegate)
    line_edit = QtWidgets.QLineEdit()
    line_edit.setAlignment(QtCore.Qt.AlignCenter)  # Center the text
    self.tarect_aldafa.setLineEdit(line_edit)
    self.tarect_aldafa.setEditable(True)
    self.tarect_aldafa.lineEdit().setReadOnly(True) 

    self.almostlm = QLineEdit(dialog)

    self.name_edit.setReadOnly(True)
    self.code_edit.setReadOnly(True)
    self.project_edit.setReadOnly(True)
    self.Amount_edit.setReadOnly(True)
    
    # تعيين التصنيف واسم العميل في الحقول
    self.code_edit.setText(self.المبلغ )
    self.name_edit.setText(self.name_العميل )
    self.project_edit.setText(self.اسم_المشروع )
    self.Amount_edit.setText(self.الباقي )
    self.amount_edit.setText(self.الباقي )
    self.tarect_aldafa.addItems(["دفع نقدًا", "تحويل بنكي", "شيك"])  

    
    # تعيين تاريخ الدفع ليكون تاريخ اليوم
    today = QDate.currentDate()
    self.date_edit.setDate(today)
    self.date_edit.setCalendarPopup(True)  # تمكين التقويم
    self.date_edit.setDisplayFormat("dd/MM/yyyy")

  

    # إنشاء تسميات الأعمدة اليمنى وضبط عرضها
    field_labels = {
        "اسم العميل:": QLabel("اسم العميل:"),
        "اسم المشروع:": QLabel("اسم المشروع:"),
        "إجمالي المبلغ:": QLabel("إجمالي المبلغ:"),
        "إجمالي الباقي:": QLabel("إجمالي البياقي:"),
        "أدخل المدفوع:": QLabel("أدخل المدفوع:"),
        "وصف المدفوع:": QLabel("وصف المدفوع:"),
        "المستلم:": QLabel("المستلم:"),
        "طريقة الدفع:": QLabel("طريقة الدفع:"),
        "تاريخ الدفعة:": QLabel("تاريخ الدفعة:")
    }

    # ضبط خصائص التسميات على اليمين
    for field_label in field_labels.values():
        field_label.setMinimumWidth(160)  # توحيد عرض التسميات
        field_label.setMinimumHeight(35)  # توحيد ارتفاع التسميات
        field_label.setAlignment(Qt.AlignCenter)

    # إضافة الحقول إلى الـ GridLayout
    grمعرف_layout.addWidget(field_labels["اسم العميل:"], 0, 0)
    grمعرف_layout.addWidget(self.name_edit, 0, 1)

    grمعرف_layout.addWidget(field_labels["اسم المشروع:"], 0, 2)
    grمعرف_layout.addWidget(self.project_edit, 0, 3)

    grمعرف_layout.addWidget(field_labels["إجمالي المبلغ:"], 2, 0)
    grمعرف_layout.addWidget(self.code_edit, 2, 1)

    grمعرف_layout.addWidget(field_labels["إجمالي الباقي:"], 2, 2)
    grمعرف_layout.addWidget(self.Amount_edit, 2, 3)

    grمعرف_layout.addWidget(field_labels["أدخل المدفوع:"], 4, 0)
    grمعرف_layout.addWidget(self.amount_edit, 4, 1,1,3)

    grمعرف_layout.addWidget(field_labels["وصف المدفوع:"], 5, 0)
    grمعرف_layout.addWidget(self.description_edit, 5, 1,1,3)

    grمعرف_layout.addWidget(field_labels["المستلم:"], 6, 0)
    grمعرف_layout.addWidget(self.almostlm, 6, 1,1,3)

    grمعرف_layout.addWidget(field_labels["طريقة الدفع:"], 7, 0)
    grمعرف_layout.addWidget(self.tarect_aldafa, 7, 1)

    grمعرف_layout.addWidget(field_labels["تاريخ الدفعة:"], 7, 2)
    grمعرف_layout.addWidget(self.date_edit, 7,3)

    
    
    self.code_edit.setAlignment(Qt.AlignCenter)
    self.name_edit.setAlignment(Qt.AlignCenter)
    self.project_edit.setAlignment(Qt.AlignCenter)
    self.Amount_edit.setAlignment(Qt.AlignCenter)
    self.amount_edit.setAlignment(Qt.AlignCenter)
    self.date_edit.setAlignment(Qt.AlignCenter)
    
    self.description_edit.setPlaceholderText("ادخل وصف الدفعة (إختياري)")
    self.description_edit.setAlignment(Qt.AlignCenter)
    self.almostlm.setPlaceholderText("ادخل اسم مستلم الدفعة (إختياري)")
    self.almostlm.setAlignment(Qt.AlignCenter)

    
    
    layout.addLayout(grمعرف_layout)

    # زر الحفظ
    save_button = QPushButton(qta.icon('fa5s.plus', color='darkgreen'), "إضافة دفعة", dialog)
    save_button.setObjectName("إضافة دفعة")  # مهم جدا: نحدد اسم الزر
    # add_btn_icon = os.path.join(icons_dir, 'add_icon.png')
    # save_button.setIcon(QIcon(add_btn_icon))
    layout.addWidget(save_button)

    # جدول لعرض الدفعات السابقة
    self.payments_table = QTableWidget(dialog)
    columns = SUP_TABLE_COLUMNS.get("المشاريع_المدفوعات", []) # Get columns from the map
    self.payments_table.setColumnCount(len(columns))
    self.payments_table.setHorizontalHeaderLabels([col["label"] for col in columns])

    self.payments_table.itemSelectionChanged.connect(lambda:display_selected_row_dofaa(self,table,self.payments_table))
    # ربط الدبل كليك بفتح نافذة التعديل
    self.payments_table.itemDoubleClicked.connect(lambda: open_edit_payment_dialog(self))

    layout.addWidget(self.payments_table)
    # تخطيط أزرار الطباعة والتعديل والحذف
    button_layout = QHBoxLayout()
    print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة الجدول")
    print_button.setMinimumSize(0, 40)
    #print_button.setIcon(QIcon(os.path.join(icons_dir, 'printer.png')))
    print_button.clicked.connect(self.print_Payments)
    button_layout.addWidget(print_button)

    # زر طباعة سند قبض
    receipt_button = QPushButton(qta.icon('fa5s.receipt', color='darkgreen'), "سند قبض")
    receipt_button.setMinimumSize(0, 40)
    receipt_button.clicked.connect(lambda: print_payment_receipt(self))
    button_layout.addWidget(receipt_button)

    delete_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف ")
    delete_button.setMinimumSize(0, 40)
    # delete_button.setIcon(QIcon(os.path.join(icons_dir, 'delete_icon.png')))
    delete_button.clicked.connect(lambda: delete_selected_row(self))
    button_layout.addWidget(delete_button)
    layout.addLayout(button_layout)
    # ربط زر الحفظ بوظيفة إضافة الدفعة
    #save_button.clicked.connect(self.save_payment)

    # تحميل بيانات الدفعات السابقة
    load_payments_table(self,self.معرف_المشروع)
    # تعيين التركيز على الحقل الأول
    self.amount_edit.setFocus()

    # حفظ المراجع المطلوبة للوصول إليها من الدوال الأخرى
    self.main_table = table
    self.main_section = section

    focus_widgets = [
        self.amount_edit,
        self.description_edit,
        self.almostlm,
        self.tarect_aldafa,
        self.date_edit
    ]
     # تطبيق التنقل
    apply_enter_focus(dialog, "إضافة دفعة",focus_widgets)
    table_setting(self.payments_table)

    
    # دالة إضافة وحفظ دفعة
    def save_payment():
        payment_date = self.date_edit.date().toString("yyyy-MM-dd")
        description = self.description_edit.text()
        amount_paid = self.amount_edit.text()
        almostlm = self.almostlm.text() or ""
        tarect_aldafa_value = self.tarect_aldafa.currentText()

        if not (amount_paid):
            QMessageBox.warning(self, 'خطأ', 'يرجى إدخال جميع الحقول المطلوبة')
            return

        try:
            amount_paid = Decimal(amount_paid)
        except ValueError:
            QMessageBox.warning(self, 'خطأ', 'المبلغ المدفوع يجب أن يكون عدد صحيح')
            return

        # بالكود التالي:
        total_amount_col = get_column_index_by_key(table, "المشاريع", "المبلغ")
        paid_amount_col = get_column_index_by_key(table, "المشاريع", "المدفوع")  

        total_amount_text = table.item(self.selected_row, total_amount_col).text()
        paid_amount_text = table.item(self.selected_row, paid_amount_col).text()

        total_amount = Decimal(total_amount_text)
        paid_amount = Decimal(0) if paid_amount_text in ["لا شيء", "", None] else Decimal(paid_amount_text)

        total_paid = paid_amount + Decimal(amount_paid)
        remaining_amount = total_amount - total_paid


        if total_paid > total_amount:
            reply = GEN_MSG_BOX('تأكيد إضافة دفعة', 'المبلغ المدفوع يتجاوز المبلغ الإجمالي، هل تريد الاستمرار؟', 'warning.png', 'استمرار', 'إلغاء', msg_box_color)
            if reply != QMessageBox.Ok:
                return

        reply = GEN_MSG_BOX('تأكيد إضافة دفعة', 'هل تريد إضافة دفعة؟', 'add_icon.png', 'إضافة دفعة', 'إلغاء', msg_box_color)
        if reply != QMessageBox.Ok:
            return

        db_name = f"project_manager_V2"

        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        # إضافة الدفعة إلى جدول المشاريع_المدفوعات
        try:
            cursor.execute("""
                INSERT INTO المشاريع_المدفوعات
                (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (self.معرف_العميل, self.معرف_المشروع, amount_paid, description, payment_date, tarect_aldafa_value, 0, almostlm, 'النظام'))

            # الحصول على id الدفعة المضافة
            payment_id = cursor.lastrowid

        except Exception as e:
            conn.rollback()
            conn.close()
            QMessageBox.critical(self, 'خطأ في قاعدة البيانات', f'فشل في إضافة الدفعة:\n{str(e)}')
            return

        # تحديث حالة المشروع بناءً على الباقي (محسوب تلقائيًا)
        # فقط إذا لم تكن الحالة محددة يدوياً من نافذة حالة المشروع
        try:
            cursor.execute(f"SELECT الوقت_المتبقي, الباقي, الحالة FROM {section} WHERE id = %s", (self.معرف_المشروع,))
            result = cursor.fetchone()
            if result:
                remaining_time, remaining_amount, current_status = result

                # تحديث الحالة فقط إذا كانت الحالة الحالية "قيد الإنجاز" أو "غير خالص"
                # لتجنب الكتابة فوق الحالات المحددة يدوياً مثل "متوقف" أو "معلق"
                if current_status in ["قيد الإنجاز", "غير خالص"]:
                    new_status = (
                        "تأكيد التسليم" if remaining_amount == 0 and "تم الإنجاز" in str(remaining_time) else
                        "قيد الإنجاز" if "تم الإنجاز" not in str(remaining_time) else
                        "غير خالص"
                    )
                    # تحديث الحالة فقط إذا كانت مختلفة
                    if current_status != new_status:
                        cursor.execute(f"UPDATE {section} SET الحالة = %s WHERE id = %s", (new_status, self.معرف_المشروع))
        except Exception as e:
            print(f"خطأ في تحديث حالة المشروع: {e}")

        conn.commit()

        # ===== الربط المحاسبي =====
        try:
            # إنشاء نظام الربط المحاسبي
            from تكامل_المحاسبة import AccountingIntegration
            accounting = AccountingIntegration()

            # الحصول على اسم العميل واسم المشروع للنظام المحاسبي
            try:
                cursor.execute("""
                    SELECT c.اسم_العميل, p.اسم_المشروع
                    FROM العملاء c
                    JOIN المشاريع p ON p.معرف_العميل = c.id
                    WHERE p.id = %s
                """, (self.معرف_المشروع,))
                result = cursor.fetchone()
                client_name = result[0] if result else "غير محدد"
                project_name = result[1] if result else "غير محدد"
            except Exception as e:
                print(f"خطأ في جلب أسماء العميل والمشروع: {e}")
                client_name = "غير محدد"
                project_name = "غير محدد"

            # إعداد بيانات الدفعة للنظام المحاسبي
            payment_data = {
                'id': payment_id,
                'معرف_المشروع': self.معرف_المشروع,
                'معرف_العميل': self.معرف_العميل,
                'وصف_المدفوع': description,
                'المبلغ_المدفوع': float(amount_paid),
                'تاريخ_الدفع': payment_date,
                'طريقة_الدفع': tarect_aldafa_value,
                'المستلم': almostlm,
                'المستخدم': 'النظام',  # يمكن تحديث هذا ليكون المستخدم الحالي
                'اسم_العميل': client_name,  # إضافة اسم العميل للنظام المحاسبي
                'اسم_المشروع': project_name  # إضافة اسم المشروع للنظام المحاسبي
            }

            # تسجيل الدفعة محاسبياً
            success, message = accounting.record_project_payment(payment_data)

            if success:
                print(f"تم تسجيل الدفعة محاسبياً: {message}")
            else:
                print(f"خطأ في التسجيل المحاسبي: {message}")
                # يمكن إضافة تحذير للمستخدم هنا إذا أردت

            accounting.close_connection()

        except Exception as e:
            print(f"خطأ في الربط المحاسبي: {e}")
            # الاستمرار حتى لو فشل الربط المحاسبي
        # ===== نهاية الربط المحاسبي =====

        # تحديث جدول الدفعات في الواجهة
        try:
            load_payments_table(self, self.معرف_المشروع)
        except Exception as e:
            print(f"خطأ في تحديث جدول الدفعات: {e}")

        # تحديث الصف الرئيسي باستخدام أسماء الأعمدة
        try:
            cursor.execute(f"SELECT * FROM {section} WHERE id = %s", (self.معرف_المشروع,))
            updated_row = cursor.fetchone()
            if updated_row:
                # الحصول على أسماء الأعمدة من قاعدة البيانات
                cursor.execute(f"DESCRIBE {section}")
                db_columns = [col[0] for col in cursor.fetchall()]

                # تحديث كل عمود باستخدام اسمه
                for db_col_idx, db_col_name in enumerate(db_columns):
                    # البحث عن فهرس العمود في الجدول بالواجهة
                    ui_col_idx = get_column_index_by_key(table, section, db_col_name)
                    if ui_col_idx >= 0 and db_col_idx < len(updated_row):
                        item = QTableWidgetItem(str(updated_row[db_col_idx]))
                        item.setTextAlignment(Qt.AlignCenter)
                        table.setItem(self.selected_row, ui_col_idx, item)
        except Exception as e:
            print(f"خطأ في تحديث الصف الرئيسي: {e}")

        conn.close()
        QMessageBox.information(self, 'نجاح', 'تم إضافة الدفعة بنجاح')

        #self.colorize_cells(table)
        
    
    # ربط زر الحفظ بوظيفة إضافة الدفعة
    save_button.clicked.connect(save_payment)
    dialog.exec()


# دالة فتح نافذة تعديل الدفعة عند الدبل كليك
def open_edit_payment_dialog(dialog_instance):
    # التحقق من وجود صف محدد
    selected_items = dialog_instance.payments_table.selectedItems()
    if not selected_items:
        QMessageBox.warning(dialog_instance, "تحذير", "يرجى تحديد الدفعة المراد تعديلها.")
        return

    selected_row = selected_items[0].row()

    # الحصول على بيانات الدفعة المحددة
    معرف_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "id")
    مبلغ_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "المبلغ_المدفوع")
    وصف_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "وصف_المدفوع")
    تاريخ_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "تاريخ_الدفع")
    طريقة_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "طريقة_الدفع")
    مستلم_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "المستلم")

    # التحقق من وجود العمود والعنصر
    if معرف_col < 0:
        QMessageBox.critical(dialog_instance, "خطأ", "تعذر العثور على عمود id.")
        return

    معرف_dofa_item = dialog_instance.payments_table.item(selected_row, معرف_col)
    if معرف_dofa_item is None:
        QMessageBox.critical(dialog_instance, "خطأ", "تعذر تحديد id الدفعة.")
        return

    معرف_dofa = معرف_dofa_item.text()

    # الحصول على البيانات الحالية
    current_amount = dialog_instance.payments_table.item(selected_row, مبلغ_col).text() if مبلغ_col >= 0 and dialog_instance.payments_table.item(selected_row, مبلغ_col) else ""
    current_description = dialog_instance.payments_table.item(selected_row, وصف_col).text() if وصف_col >= 0 and dialog_instance.payments_table.item(selected_row, وصف_col) else ""
    current_date = dialog_instance.payments_table.item(selected_row, تاريخ_col).text() if تاريخ_col >= 0 and dialog_instance.payments_table.item(selected_row, تاريخ_col) else ""
    current_method = dialog_instance.payments_table.item(selected_row, طريقة_col).text() if طريقة_col >= 0 and dialog_instance.payments_table.item(selected_row, طريقة_col) else ""
    current_receiver = dialog_instance.payments_table.item(selected_row, مستلم_col).text() if مستلم_col >= 0 and dialog_instance.payments_table.item(selected_row, مستلم_col) else ""

    # إنشاء نافذة التعديل
    edit_dialog = QDialog(dialog_instance)
    edit_dialog.setWindowTitle("تعديل الدفعة")
    edit_dialog.resize(500, 400)
    edit_dialog.setLayoutDirection(Qt.RightToLeft)
    edit_dialog.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)

    layout = QVBoxLayout(edit_dialog)

    # إنشاء النموذج
    form_layout = QGridLayout()

    # حقل المبلغ
    amount_label = QLabel("المبلغ المدفوع:")
    amount_edit = QLineEdit()
    amount_edit.setText(current_amount)
    amount_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(amount_label, 0, 0)
    form_layout.addWidget(amount_edit, 0, 1)

    # حقل الوصف
    description_label = QLabel("وصف المدفوع:")
    description_edit = QLineEdit()
    description_edit.setText(current_description)
    description_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(description_label, 1, 0)
    form_layout.addWidget(description_edit, 1, 1)

    # حقل التاريخ
    date_label = QLabel("تاريخ الدفع:")
    date_edit = QDateEdit()
    if current_date:
        date_edit.setDate(QDate.fromString(current_date, 'yyyy-MM-dd'))
    else:
        date_edit.setDate(QDate.currentDate())
    date_edit.setCalendarPopup(True)
    date_edit.setDisplayFormat("dd/MM/yyyy")
    date_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(date_label, 2, 0)
    form_layout.addWidget(date_edit, 2, 1)

    # حقل طريقة الدفع
    method_label = QLabel("طريقة الدفع:")
    method_combo = QComboBox()
    method_combo.addItems(["دفع نقدًا", "تحويل بنكي", "شيك"])
    if current_method:
        method_combo.setCurrentText(current_method)
    delegate = AlignedItemDelegate(method_combo)
    method_combo.setItemDelegate(delegate)
    line_edit = QtWidgets.QLineEdit()
    line_edit.setAlignment(QtCore.Qt.AlignCenter)
    method_combo.setLineEdit(line_edit)
    method_combo.setEditable(True)
    method_combo.lineEdit().setReadOnly(True)
    form_layout.addWidget(method_label, 3, 0)
    form_layout.addWidget(method_combo, 3, 1)

    # حقل المستلم
    receiver_label = QLabel("المستلم:")
    receiver_edit = QLineEdit()
    receiver_edit.setText(current_receiver)
    receiver_edit.setAlignment(Qt.AlignCenter)
    form_layout.addWidget(receiver_label, 4, 0)
    form_layout.addWidget(receiver_edit, 4, 1)

    layout.addLayout(form_layout)

    # أزرار الحفظ والإلغاء
    button_layout = QHBoxLayout()
    save_button = QPushButton(qta.icon('fa5s.save', color='darkgreen'), "حفظ التعديل")
    cancel_button = QPushButton(qta.icon('fa5s.times', color='crimson'), "إلغاء")

    button_layout.addWidget(save_button)
    button_layout.addWidget(cancel_button)
    layout.addLayout(button_layout)

    # دالة الحفظ
    def save_changes():
        new_amount = amount_edit.text()
        new_description = description_edit.text()
        new_date = date_edit.date().toString("yyyy-MM-dd")
        new_method = method_combo.currentText()
        new_receiver = receiver_edit.text() or ""

        if not new_amount:
            QMessageBox.warning(edit_dialog, "خطأ", "يرجى إدخال المبلغ المدفوع.")
            return

        try:
            new_amount = int(new_amount)
        except ValueError:
            QMessageBox.warning(edit_dialog, "خطأ", "المبلغ المدفوع يجب أن يكون رقمًا صحيحًا.")
            return

        # تحديث قاعدة البيانات
        db_name = f"project_manager_V2"
        conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
        cursor = conn.cursor()

        cursor.execute("UPDATE المشاريع_المدفوعات SET تاريخ_الدفع = %s, وصف_المدفوع = %s, المبلغ_المدفوع = %s, طريقة_الدفع = %s, المستلم = %s WHERE id = %s",
                      (new_date, new_description, new_amount, new_method, new_receiver, معرف_dofa))

        # تحديث حالة المشروع
        # فقط إذا لم تكن الحالة محددة يدوياً من نافذة حالة المشروع
        معرف_المشروع = dialog_instance.معرف_المشروع
        cursor.execute("SELECT الوقت_المتبقي, الباقي, الحالة FROM المشاريع WHERE id = %s", (معرف_المشروع,))
        remaining_time, remaining_amount, current_status = cursor.fetchone()

        # تحديث الحالة فقط إذا كانت الحالة الحالية "قيد الإنجاز" أو "غير خالص"
        if current_status in ["قيد الإنجاز", "غير خالص"]:
            new_status = (
                "تأكيد التسليم" if remaining_amount == 0 and "تم الإنجاز" in str(remaining_time) else
                "قيد الإنجاز" if "تم الإنجاز" not in str(remaining_time) else
                "غير خالص"
            )
            if current_status != new_status:
                cursor.execute("UPDATE المشاريع SET الحالة = %s WHERE id = %s", (new_status, معرف_المشروع))

        conn.commit()
        conn.close()

        # تحديث جدول الدفعات
        load_payments_table(dialog_instance, معرف_المشروع)

        # تحديث الواجهة الرئيسية
        if hasattr(dialog_instance, 'main_table') and hasattr(dialog_instance, 'main_section'):
            table_name = dialog_instance.main_section
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()
            cursor.execute(f"SELECT * FROM {table_name} WHERE id = %s", (معرف_المشروع,))
            updated_row = cursor.fetchone()
            if updated_row:
                cursor.execute(f"DESCRIBE {table_name}")
                db_columns = [col[0] for col in cursor.fetchall()]
                for db_col_idx, db_col_name in enumerate(db_columns):
                    ui_col_idx = get_column_index_by_key(dialog_instance.main_table, table_name, db_col_name)
                    if ui_col_idx >= 0 and db_col_idx < len(updated_row):
                        item = QTableWidgetItem(str(updated_row[db_col_idx]))
                        item.setTextAlignment(Qt.AlignCenter)
                        dialog_instance.main_table.setItem(dialog_instance.selected_row, ui_col_idx, item)
            conn.close()

        

        QMessageBox.information(edit_dialog, "نجاح", f"تم تعديل الدفعة رقم {معرف_dofa} بنجاح.")
        edit_dialog.accept()

    # ربط الأزرار
    save_button.clicked.connect(save_changes)
    cancel_button.clicked.connect(edit_dialog.reject)

    # تطبيق التنقل بين الحقول
    focus_widgets = [amount_edit, description_edit, receiver_edit, method_combo, date_edit]
    apply_enter_focus(edit_dialog, "حفظ التعديل", focus_widgets)

    # عرض النافذة
    edit_dialog.exec()

# دالة حذف دفعة
def delete_selected_row(dialog_instance):
    selected_items = dialog_instance.payments_table.selectedItems()
    if not selected_items:
        QMessageBox.warning(dialog_instance, "تحذير", "يرجى تحديد الصف المراد حذفه.")
        return

    selected_row = selected_items[0].row()

    # الحصول على فهارس الأعمدة باستخدام أسماء الأعمدة
    معرف_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "id")
    رقم_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "الرقم")

    # التحقق من وجود العمود والعنصر
    if معرف_col < 0:
        QMessageBox.critical(dialog_instance, "خطأ", "تعذر العثور على عمود id.")
        return

    dofa_معرف_item = dialog_instance.payments_table.item(selected_row, معرف_col)
    if dofa_معرف_item is None:
        QMessageBox.critical(dialog_instance, "خطأ", "تعذر تحديد id الدفعة.")
        return

    dofa_id = dofa_معرف_item.text()

    # الحصول على رقم الدفعة للعرض
    record_number = ""
    if رقم_col >= 0:
        record_number_item = dialog_instance.payments_table.item(selected_row, رقم_col)
        if record_number_item:
            record_number = record_number_item.text()

    # استخدام id المشروع بدلاً من id العميل
    معرف_المشروع = dialog_instance.معرف_المشروع
    db_name = f"project_manager_V2"

    reply = GEN_MSG_BOX('تأكيد حذف دفعة', f"هل أنت متأكد أنك تريد حذف الدفعة رقم {record_number}؟", 'warning.png', 'حذف', 'إلغاء', msg_box_color)
    if reply != QMessageBox.Ok:
        return

    conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
    cursor = conn.cursor()

    cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (dofa_id,))

    # تحديث حالة المشروع بناءً على الباقي (محسوب تلقائيًا)
    # فقط إذا لم تكن الحالة محددة يدوياً من نافذة حالة المشروع
    cursor.execute("SELECT الوقت_المتبقي, الباقي, الحالة FROM المشاريع WHERE id = %s", (معرف_المشروع,))
    remaining_time, remaining_amount, current_status = cursor.fetchone()

    # تحديث الحالة فقط إذا كانت الحالة الحالية "قيد الإنجاز" أو "غير خالص"
    if current_status in ["قيد الإنجاز", "غير خالص"]:
        new_status = (
            "تأكيد التسليم" if remaining_amount == 0 and "تم الإنجاز" in str(remaining_time) else
            "قيد الإنجاز" if "تم الإنجاز" not in str(remaining_time) else
            "غير خالص"
        )
        # تحديث الحالة فقط إذا كانت مختلفة
        if current_status != new_status:
            cursor.execute("UPDATE المشاريع SET الحالة = %s WHERE id = %s", (new_status, معرف_المشروع))

    conn.commit()

    # تحديث جدول الدفعات أولاً
    load_payments_table(dialog_instance, معرف_المشروع)

    # تحديث الصف الرئيسي باستخدام أسماء الأعمدة
    if hasattr(dialog_instance, 'main_table') and hasattr(dialog_instance, 'main_section'):
        table_name = dialog_instance.main_section  # استخدام اسم الجدول المحفوظ
        cursor.execute(f"SELECT * FROM {table_name} WHERE id = %s", (معرف_المشروع,))
        updated_row = cursor.fetchone()
        if updated_row:
            # الحصول على أسماء الأعمدة من قاعدة البيانات
            cursor.execute(f"DESCRIBE {table_name}")
            db_columns = [col[0] for col in cursor.fetchall()]

            # تحديث كل عمود باستخدام اسمه
            for db_col_idx, db_col_name in enumerate(db_columns):
                # البحث عن فهرس العمود في الجدول بالواجهة
                ui_col_idx = get_column_index_by_key(dialog_instance.main_table, table_name, db_col_name)
                if ui_col_idx >= 0 and db_col_idx < len(updated_row):
                    item = QTableWidgetItem(str(updated_row[db_col_idx]))
                    item.setTextAlignment(Qt.AlignCenter)
                    dialog_instance.main_table.setItem(dialog_instance.selected_row, ui_col_idx, item)

    conn.close()

    
    # إظهار رسالة النجاح في النهاية
    QMessageBox.information(dialog_instance, "نجاح", "تم حذف الدفعة بنجاح وتحديث المدفوع والباقي.")

# دالة تحميل جدول الدفعات
def load_payments_table(self,معرف_المشروع):

    db_name = f"project_manager_V2"

    conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
    cursor = conn.cursor()

    # استخدام JOIN لجلب بيانات الدفعات مع أسماء العملاء والمشاريع
    cursor.execute("""
        SELECT dp.id, dp.معرف_العميل, dp.معرف_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع,
               dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, dp.خصم, dp.المستخدم, dp.تاريخ_الإضافة, dp.السنة,
               c.اسم_العميل, p.اسم_المشروع
        FROM المشاريع_المدفوعات dp
        LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
        LEFT JOIN العملاء c ON p.معرف_العميل = c.id
        WHERE dp.معرف_المشروع = %s
        ORDER BY dp.id DESC
    """, (معرف_المشروع,))
    raw_payments = cursor.fetchall()

    payments = [tuple("" if val is None else val for val in row) for row in raw_payments]

    self.payments_table.setRowCount(0)

    total_rows = len(payments)
    for row_idx, row in enumerate(payments):
        row_position = self.payments_table.rowCount()
        self.payments_table.insertRow(row_position)

        row_number = total_rows - row_idx

        # الحصول على فهارس الأعمدة من SUP_TABLE_COLUMNS
        معرف_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "id")
        رقم_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "الرقم")
        مبلغ_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "المبلغ_المدفوع")
        تاريخ_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "تاريخ_الدفع")
        وصف_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "وصف_المدفوع")
        طريقة_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "طريقة_الدفع")
        مستلم_col = get_sup_column_index_by_key(self.payments_table, "المشاريع_المدفوعات", "المستلم")

        # إنشاء العناصر وتعيينها في المواضع الصحيحة
        if معرف_col >= 0:
            item = QTableWidgetItem(str(row[0]))  # id من قاعدة البيانات
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, معرف_col, item)

        if رقم_col >= 0:
            item = QTableWidgetItem(str(row_number))  # الرقم المحسوب
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, رقم_col, item)

        if مبلغ_col >= 0:
            item = QTableWidgetItem(str(row[4]))  # المبلغ_المدفوع من قاعدة البيانات
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, مبلغ_col, item)

        if تاريخ_col >= 0:
            item = QTableWidgetItem(str(row[5]))  # تاريخ_الدفع من قاعدة البيانات
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, تاريخ_col, item)

        if وصف_col >= 0:
            item = QTableWidgetItem(str(row[3]))  # وصف_المدفوع من قاعدة البيانات
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, وصف_col, item)

        if طريقة_col >= 0:
            item = QTableWidgetItem(str(row[6]))  # طريقة_الدفع من قاعدة البيانات
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, طريقة_col, item)

        if مستلم_col >= 0:
            item = QTableWidgetItem(str(row[7]))  # المستلم من قاعدة البيانات
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payments_table.setItem(row_position, مستلم_col, item)

    conn.close()
    table_setting(self.payments_table)

# دالة عرض بيانات الدفعة المحدثة لاستخدام أسماء الأعمدة
def display_selected_row_dofaa(self,table,payments_table):
    selected_row = payments_table.currentRow()
    if selected_row != -1:
        # الحصول على فهارس الأعمدة من SUP_TABLE_COLUMNS
        مبلغ_col = get_sup_column_index_by_key(payments_table, "المشاريع_المدفوعات", "المبلغ_المدفوع")
        وصف_col = get_sup_column_index_by_key(payments_table, "المشاريع_المدفوعات", "وصف_المدفوع")
        تاريخ_col = get_sup_column_index_by_key(payments_table, "المشاريع_المدفوعات", "تاريخ_الدفع")
        طريقة_col = get_sup_column_index_by_key(payments_table, "المشاريع_المدفوعات", "طريقة_الدفع")
        مستلم_col = get_sup_column_index_by_key(payments_table, "المشاريع_المدفوعات", "المستلم")

        # تعبئة الحقول باستخدام فهارس الأعمدة الصحيحة
        if مبلغ_col >= 0 and payments_table.item(selected_row, مبلغ_col):
            self.amount_edit.setText(payments_table.item(selected_row, مبلغ_col).text())

        if وصف_col >= 0 and payments_table.item(selected_row, وصف_col):
            self.description_edit.setText(payments_table.item(selected_row, وصف_col).text())

        if تاريخ_col >= 0 and payments_table.item(selected_row, تاريخ_col):
            self.date_edit.setDate(QDate.fromString(payments_table.item(selected_row, تاريخ_col).text(), 'yyyy-MM-dd'))

        if طريقة_col >= 0 and payments_table.item(selected_row, طريقة_col):
            self.tarect_aldafa.setCurrentText(payments_table.item(selected_row, طريقة_col).text())

        if مستلم_col >= 0 and payments_table.item(selected_row, مستلم_col):
            self.almostlm.setText(payments_table.item(selected_row, مستلم_col).text())

#---------------تقارير الدفعات =========================================================================
def reports_Payments(self):
    dialog = QDialog(self)
    dialog.setWindowTitle("تقارير الدفعات")
    dialog.resize(1200, 650)
    dialog.setLayoutDirection(QtCore.Qt.RightToLeft)
    dialog.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
    
    layout = QVBoxLayout(dialog)
    
    # إضافة خيارات البحث والسنة
    search_layout = QHBoxLayout()
    search_label = QLabel("بحث:")
    search_label.setFixedSize(100, 30)
    search_label.setAlignment(Qt.AlignCenter)
    self.search_lineEdit = QLineEdit()
    self.search_lineEdit.setPlaceholderText("ادخل اسم العميل أو المبلغ أو تاريخ او اسم المشروع")
    self.search_lineEdit.textChanged.connect(self.filter_reports_Payments)
    self.search_lineEdit.setAlignment(Qt.AlignCenter)
    
    year_label = QLabel("السنة:")
    year_label.setAlignment(Qt.AlignCenter)
    year_label.setFixedSize(100, 30)
    self.year_combo1 = QComboBox()
    self.populate_years(self.year_combo1) 
    self.populate_years1(self.year_combo1) 
    #self.populate_years1_Payments() 
    self.year_combo1.currentTextChanged.connect(self.load_reports_all_Payments)
    self.year_combo1.lineEdit().mousePressEvent = lambda event: open_combo(self.year_combo1, event)
    
    search_layout.addWidget(search_label)
    search_layout.addWidget(self.search_lineEdit)
    search_layout.addWidget(year_label)
    search_layout.addWidget(self.year_combo1)
    layout.addLayout(search_layout)
    
    total_layout = QHBoxLayout()
    # إضافة Labels لعرض إجمالي الباقي، المصروفات، والمدفوعات
    self.payment_total_label = QLabel('إجمالي الدفعات: 0', self)
    self.payment_total_label.setStyleSheet("background-color: #7eb995; padding: 5px;")
    self.payment_total_label.setAlignment(Qt.AlignCenter)
    total_layout.addWidget(self.payment_total_label)

    # إضافة الصف إلى التخطيط الرئيسي
    layout.addLayout(total_layout)
    
    # إضافة الجدول
    self.reports_table = QTableWidget()
    headers = ["الرقم", "التصنيف", "     اسم العميل     ", "      المشروع      ", "           وصف المدفوع           ", " المبلغ المدفوع ", "   تاريخ الدفعة   ", " طريقة الدفع ", " المستلم "]
    add_table_column(self.reports_table,headers)
    layout.addWidget(self.reports_table)

    self.reports_table.setColumnHidden(1, True)

    # إضافة تخطيط أفقي لزر الطباعة وزر الحذف
    button_layout = QHBoxLayout()
    # زر الطباعة
    print_button = QPushButton(qta.icon('fa5s.print', color='navy'), "طباعة تقرير ")
    print_button.setMinimumSize(QtCore.QSize(0, 40))
    print_button.setLayoutDirection(QtCore.Qt.RightToLeft)
    print_button.clicked.connect(self.print_reports_Payments)
    # print_button_icon = os.path.join(icons_dir, 'printer.png')
    # print_button.setIcon(QIcon(print_button_icon))
    button_layout.addWidget(print_button)
    # # زر الحذف
    # delete_button = QPushButton(qta.icon('fa5s.trash', color='crimson'), "حذف ")
    # delete_button.setMinimumSize(QtCore.QSize(0, 40))
    # delete_button.setLayoutDirection(QtCore.Qt.RightToLeft)
    # delete_button.clicked.connect(self.delete_selected_row)
    # button_layout.addWidget(delete_button)

    # إضافة التخطيط الأفقي إلى التخطيط الرئيسي
    layout.addLayout(button_layout)
    
    dialog.setLayout(layout)
    self.load_reports_all_Payments()
    self.all_Payments_calculate_totals()
    dialog.exec()
    

#السنة للتقارير
def populate_years1_Payments(self):
    selected_year = self.year_combo.currentText()
    project_dir = folder_path
    if not os.path.exists(project_dir):
        os.makedirs(project_dir)
    pattern = re.compile(r'database_(\d{4})')
    current_year =selected_year
    years = set()
    for file_name in os.listdir(project_dir):
        match = pattern.match(file_name)
        if match:
            years.add(int(match.group(1)))
    if current_year not in years:
        years.add(current_year)
    years = sorted(years, reverse=True)
    for year in years:
        self.year_combo1.addItem(str(year))
    self.year_combo1.setCurrentText(str(current_year))    
    self.year_combo1.setItemDelegate(AlignedItemDelegate(self.year_combo1))
    
    line_edit = QtWidgets.QLineEdit()
    line_edit.setAlignment(QtCore.Qt.AlignCenter)  # Center the text
    self.year_combo1.setLineEdit(line_edit)
    self.year_combo1.setEditable(True)
    self.year_combo1.lineEdit().setReadOnly(True) 

    
def filter_reports_Payments(self):
    # تطبيق منطق التصفية هنا بناءً على النص المدخل في self.search_lineEdit
    search_text = self.search_lineEdit.text().lower()
    
    for row in range(self.reports_table.rowCount()):
        item_col1 = self.reports_table.item(row, 1)  # العمود الثاني (اسم الموظف)
        item_col2 = self.reports_table.item(row, 2)  # العمود الثاني (اسم الموظف)
        item_col3 = self.reports_table.item(row, 3)  # العمود الثاني (اسم الموظف)
        item_col4 = self.reports_table.item(row, 4)  # العمود الثاني (اسم الموظف)
        item_col5 = self.reports_table.item(row, 5)  # العمود الرابع
        
        # التحقق من النص في الأعمدة 2، 4، و 5
        if ((item_col2 and search_text in item_col2.text().lower()) or
            (item_col1 and search_text in item_col1.text().lower()) or
            (item_col3 and search_text in item_col3.text().lower()) or
            (item_col4 and search_text in item_col4.text().lower()) or
            (item_col5 and search_text in item_col5.text().lower())):
            self.reports_table.setRowHidden(row, False)
        else:
            self.reports_table.setRowHidden(row, True)
    self.all_Payments_calculate_totals()
    colorize(self.reports_table,"#cdd7b9")


def load_reports_all_Payments(self):
    selected_year = self.year_combo1.currentText()
    db_name = f"project_manager_V2"
    try:
        conn = mysql.connector.connect(host=host,user=user,password=password,database=db_name)
        cursor = conn.cursor()
        #cursor.execute("SELECT id, المبلغ_المدفوع, الوصف, تاريخ_الدفع, اسم_العميل, التصنيف FROM المشاريع_المدفوعات")
        #cursor.execute("SELECT id, التصنيف, اسم_العميل, اسم_المشروع, المبلغ_المدفوع, تاريخ_الدفع,وصف_المدفوع,طريقة_الدفع, المستلم FROM المشاريع_المدفوعات")
        cursor.execute("""
            SELECT dp.id, c.اسم_العميل, p.اسم_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع,
                   dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, p.التصنيف
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
            LEFT JOIN العملاء c ON p.معرف_العميل = c.id
            WHERE YEAR(dp.تاريخ_الدفع) = %s OR %s = 'الكل'
            ORDER BY dp.id DESC
        """, (selected_year, selected_year))
        reports = cursor.fetchall()
        # تنظيف البيانات دفعة واحدة: أي None يصبح ""
        payments = [tuple("" if val is None else val for val in row) for row in reports]
        self.reports_table.setRowCount(0)

        total_reports = len(reports)  # عدد السجلات

        for row_number, row_data in enumerate(reversed(payments)):
            self.reports_table.insertRow(row_number)

            # العمود الأول: الترقيم بالعكس
            serial_number = total_reports - row_number
            serial_item = QTableWidgetItem(str(serial_number))
            serial_item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 0, serial_item)


            # إدراج البيانات في الأعمدة المناسبة
            # العمود 1: التصنيف (مخفي)
            item = QTableWidgetItem(str(row_data[8]) if row_data[8] else "")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 1, item)

            # العمود 2: اسم العميل
            item = QTableWidgetItem(str(row_data[1]) if row_data[1] else "غير محدد")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 2, item)

            # العمود 3: اسم المشروع
            item = QTableWidgetItem(str(row_data[2]) if row_data[2] else "غير محدد")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 3, item)

            # العمود 4: وصف المدفوع
            item = QTableWidgetItem(str(row_data[3]) if row_data[3] else "")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 4, item)

            # العمود 5: المبلغ المدفوع
            item = QTableWidgetItem(str(row_data[4]) if row_data[4] else "0")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 5, item)

            # العمود 6: تاريخ الدفعة
            item = QTableWidgetItem(str(row_data[5]) if row_data[5] else "")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 6, item)

            # العمود 7: طريقة الدفع
            item = QTableWidgetItem(str(row_data[6]) if row_data[6] else "")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 7, item)

            # العمود 8: المستلم
            item = QTableWidgetItem(str(row_data[7]) if row_data[7] else "")
            item.setTextAlignment(Qt.AlignCenter)
            self.reports_table.setItem(row_number, 8, item)
                
        conn.close()
        self.filter_reports_Payments()
        colorize(self.reports_table,"#cdd7b9")
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    table_setting(self.reports_table)


   
def all_Payments_calculate_totals(self):
    """حساب الإجماليات من الجداول"""
    self.total_paid = 0
    # حساب الإجمالي من جدول الدفعات
    for row in range(self.reports_table.rowCount()):
        if self.reports_table.isRowHidden(row):
            continue
    
        payment_item = self.reports_table.item(row, 5)  # العمود الذي يحتوي على المدفوعات في جدول الدفعات
        if payment_item:
            self.total_paid += int(payment_item.text()) if payment_item.text()!= "خالص" else 0
    # تحديث المسمى التوضيحي للإجماليات في الواجهة
    self.payment_total_label.setText(f"إجمالي الدفعات: {self.total_paid} {Currency_type}")

#تلوين الخلايا في الجدول
def colorize(table,green):
    col_idx = 5
    col_idx1 = 6
    col_idx2 = 7
    col_idx3 = 9
    for row_idx in range(table.rowCount()):
        item4 = table.item(row_idx, col_idx)
        item6 = table.item(row_idx, col_idx1)
        item7 = table.item(row_idx, col_idx2)
        item9 = table.item(row_idx, col_idx3)
        if item4:
            col_data = item4.text()
            item4.setForeground(QColor(green))
            

        # if item6:
        #     col_data5 = item4.text()
        #     col_data6 = item6.text()
        #     if col_data5 == col_data6 and col_data5 != "0":
        #         item6.setText("خالص")  # تحويل "مدين" إلى "0"  
        #     #if "خالص" in col_data6:
        #         item6.setForeground(QColor(green))
        #     else:
        #         item6.setForeground(QColor(yellow))
        # if item7:
        #     col_data5 = item6.text()
        #     col_data7 = item7.text()
            
        #     if col_data7 == "0" and col_data5 != "0":
        #         item7.setText("خالص")  # تحويل "مدين" إلى "0"  
        #     #if "خالص" in col_data7:
        #         item7.setForeground(QColor(green))
        #     else:
        #         item7.setForeground(QColor(red))

    # # البحث عن العمود الذي يحمل اسم "نوع الحساب"
    # col_idx = -1
    # for col in range(table.columnCount()):
    #     header_item = table.horizontalHeaderItem(col)
    #     if header_item and header_item.text() == "نوع الحساب":
    #         col_idx = col
    #         break

    # # إذا تم العثور على العمود، يتم تلوينه
    # if col_idx != -1:
    #     for row_idx in range(table.rowCount()):
    #         item = table.item(row_idx, col_idx)
    #         if item:
    #             col_data = item.text().strip()
    #             if col_data == "دائن":
    #                 item.setForeground(QColor("#6e989c"))
    #             elif col_data == "مدين":
    #                 item.setForeground(QColor("#e4c19b"))

# دالة طباعة سند قبض للدفعة المحددة ===============================================================
def print_payment_receipt(dialog_instance):
    """طباعة سند قبض للدفعة المحددة في نصف صفحة A4"""
    # التحقق من وجود صف محدد
    selected_items = dialog_instance.payments_table.selectedItems()
    if not selected_items:
        QMessageBox.warning(dialog_instance, "تحذير", "يرجى تحديد الدفعة المراد طباعة سند قبض لها.")
        return

    selected_row = selected_items[0].row()

    # الحصول على بيانات الدفعة المحددة
    try:
        معرف_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "id")
        رقم_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "الرقم")
        مبلغ_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "المبلغ_المدفوع")
        وصف_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "وصف_المدفوع")
        تاريخ_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "تاريخ_الدفع")
        طريقة_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "طريقة_الدفع")
        مستلم_col = get_sup_column_index_by_key(dialog_instance.payments_table, "المشاريع_المدفوعات", "المستلم")

        # التحقق من وجود العمود والعنصر
        if معرف_col < 0:
            QMessageBox.critical(dialog_instance, "خطأ", "تعذر العثور على عمود id.")
            return

        # الحصول على البيانات
        payment_id = dialog_instance.payments_table.item(selected_row, معرف_col).text() if dialog_instance.payments_table.item(selected_row, معرف_col) else ""
        payment_number = dialog_instance.payments_table.item(selected_row, رقم_col).text() if رقم_col >= 0 and dialog_instance.payments_table.item(selected_row, رقم_col) else ""
        payment_amount = dialog_instance.payments_table.item(selected_row, مبلغ_col).text() if مبلغ_col >= 0 and dialog_instance.payments_table.item(selected_row, مبلغ_col) else ""
        payment_description = dialog_instance.payments_table.item(selected_row, وصف_col).text() if وصف_col >= 0 and dialog_instance.payments_table.item(selected_row, وصف_col) else ""
        payment_date = dialog_instance.payments_table.item(selected_row, تاريخ_col).text() if تاريخ_col >= 0 and dialog_instance.payments_table.item(selected_row, تاريخ_col) else ""
        payment_method = dialog_instance.payments_table.item(selected_row, طريقة_col).text() if طريقة_col >= 0 and dialog_instance.payments_table.item(selected_row, طريقة_col) else ""
        payment_receiver = dialog_instance.payments_table.item(selected_row, مستلم_col).text() if مستلم_col >= 0 and dialog_instance.payments_table.item(selected_row, مستلم_col) else ""

        # بيانات العميل والمشروع
        client_name = dialog_instance.name_العميل
        project_name = dialog_instance.اسم_المشروع

        # إنشاء سند القبض
        create_payment_receipt_html(
            payment_id=payment_id,
            payment_number=payment_number,
            payment_amount=payment_amount,
            payment_description=payment_description,
            payment_date=payment_date,
            payment_method=payment_method,
            payment_receiver=payment_receiver,
            client_name=client_name,
            project_name=project_name,
            dialog_instance=dialog_instance
        )

    except Exception as e:
        QMessageBox.critical(dialog_instance, "خطأ", f"حدث خطأ أثناء إنشاء سند القبض:\n{str(e)}")

def create_payment_receipt_html(payment_id, payment_number, payment_amount, payment_description,
                               payment_date, payment_method, payment_receiver, client_name,
                               project_name, dialog_instance):
    """إنشاء ملف HTML لسند القبض"""
    from datetime import datetime

    current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

    # تحويل المبلغ إلى كلمات (يمكن إضافة دالة تحويل الأرقام إلى كلمات هنا)
    amount_in_words = f"فقط {payment_amount} {Currency_type} لا غير"

    html_content = f"""
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>سند قبض رقم {payment_number}</title>
        <style>
            @media print {{
                @page {{
                    size: A4 portrait;
                    margin: 15mm 10mm 15mm 10mm;
                }}
                body {{
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }}
                .no-print {{
                    display: none !important;
                }}
            }}

            * {{
                box-sizing: border-box;
            }}

            body {{
                font-family: {print_font}, Arial, sans-serif;
                direction: rtl;
                margin: 15mm 10mm;
                padding: 0;
                font-size: 14px;
                line-height: 1.4;
                color: #333;
                background: white;
            }}

            .receipt-container {{
                width: 100%;
                max-width: 210mm;
                margin: 0 auto;
                border: 3px solid #333;
                border-radius: 15px;
                background-color: #fff;
                min-height: 250mm;
                position: relative;
                padding: 25px;
            }}

            .header-section {{
                border-bottom: 3px solid #333;
                padding-bottom: 20px;
                margin-bottom: 25px;
            }}

            .top-info {{
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 15px;
            }}

            .date-info {{
                font-size: 12px;
                color: #666;
                text-align: left;
                direction: ltr;
            }}

            .page-info {{
                font-size: 12px;
                color: #666;
                text-align: right;
            }}

            .company-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }}

            .company-logo {{
                width: 80px;
                height: 80px;
                object-fit: contain;
            }}

            .company-name {{
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                text-align: center;
                flex-grow: 1;
            }}

            .receipt-title {{
                font-size: 32px;
                font-weight: bold;
                color: #e74c3c;
                text-align: center;
                margin: 20px 0;
                text-decoration: underline;
            }}

            .receipt-number {{
                font-size: 18px;
                color: #333;
                text-align: center;
                margin-bottom: 20px;
                font-weight: bold;
            }}

            .content-section {{
                margin: 30px 0;
            }}

            .info-table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}

            .info-table td {{
                padding: 15px 20px;
                border-bottom: 2px solid #ddd;
                font-size: 16px;
            }}

            .info-label {{
                font-weight: bold;
                color: #2c3e50;
                width: 35%;
                text-align: right;
                background-color: #f8f9fa;
            }}

            .info-value {{
                color: #333;
                text-align: right;
                font-weight: 500;
            }}

            .amount-section {{
                background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
                border: 3px solid #e74c3c;
                border-radius: 15px;
                padding: 25px;
                margin: 30px 0;
                text-align: center;
            }}

            .amount-number {{
                font-size: 36px;
                font-weight: bold;
                color: #e74c3c;
                margin-bottom: 10px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }}

            .amount-words {{
                font-size: 16px;
                color: #666;
                font-style: italic;
                font-weight: 500;
            }}

            .signatures-section {{
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
            }}

            .signature-box {{
                text-align: center;
                width: 40%;
            }}

            .signature-label {{
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 50px;
            }}

            .signature-line {{
                border-top: 2px solid #333;
                width: 100%;
                margin-top: 50px;
                padding-top: 8px;
                font-size: 14px;
                color: #666;
            }}

            .footer-section {{
                position: absolute;
                bottom: 20px;
                left: 25px;
                right: 25px;
                border-top: 2px solid #333;
                padding-top: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }}

            .company-contact {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
            }}

            .contact-item {{
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 12px;
                color: #555;
            }}

            .print-info {{
                text-align: center;
                font-size: 10px;
                color: #999;
                margin-top: 10px;
            }}
        </style>
        <script>
            window.onload = function() {{
                window.print();
                window.onafterprint = function() {{
                    window.close();
                }};
            }}
        </script>
    </head>
    <body>
        <div class="receipt-container">
            <!-- معلومات الرأس -->
            <div class="header-section">
                <div class="top-info">
                    <div class="page-info">سند قبض رقم {payment_number}</div>
                    <div class="date-info">{current_time}</div>
                </div>

                <div class="company-header">
                    {f'<img src="{logo_path}" class="company-logo" alt="شعار الشركة">' if logo_path else '<div style="width: 80px;"></div>'}
                    <div class="company-name">{company_name}</div>
                    <div style="width: 80px;"></div> <!-- للتوازن -->
                </div>

                <div class="receipt-title">سند قبض</div>
                <div class="receipt-number">رقم السند: {payment_number}</div>
            </div>

            <!-- محتوى السند -->
            <div class="content-section">
                <table class="info-table">
                    <tr>
                        <td class="info-label">اسم العميل:</td>
                        <td class="info-value">{client_name}</td>
                    </tr>
                    <tr>
                        <td class="info-label">اسم المشروع:</td>
                        <td class="info-value">{project_name}</td>
                    </tr>
                    <tr>
                        <td class="info-label">تاريخ الاستلام:</td>
                        <td class="info-value">{payment_date}</td>
                    </tr>
                    <tr>
                        <td class="info-label">طريقة الدفع:</td>
                        <td class="info-value">{payment_method}</td>
                    </tr>
                    <tr>
                        <td class="info-label">وصف الدفعة:</td>
                        <td class="info-value">{payment_description if payment_description else 'دفعة من المشروع'}</td>
                    </tr>
                    {f'''<tr>
                        <td class="info-label">المستلم:</td>
                        <td class="info-value">{payment_receiver}</td>
                    </tr>''' if payment_receiver else ''}
                </table>

                <!-- قسم المبلغ -->
                <div class="amount-section">
                    <div class="amount-number">{payment_amount} {Currency_type}</div>
                    <div class="amount-words">{amount_in_words}</div>
                </div>

                <!-- قسم التوقيعات -->
                <div class="signatures-section">
                    <div class="signature-box">
                        <div class="signature-label">توقيع المستلم</div>
                        <div class="signature-line">________________</div>
                    </div>

                    <div class="signature-box">
                        <div class="signature-label">توقيع المحاسب</div>
                        <div class="signature-line">________________</div>
                    </div>
                </div>
            </div>

            <!-- معلومات التواصل في الأسفل -->
            <div class="footer-section">
                <div class="company-contact">
                    <div class="contact-item">
                        <span>📍</span>
                        <span>العنوان: {company_address}</span>
                    </div>
                    <div class="contact-item">
                        <span>📞</span>
                        <span>الهاتف: {company_phone}</span>
                    </div>
                    <div class="contact-item">
                        <span>✉</span>
                        <span>البريد الإلكتروني: {company_email}</span>
                    </div>
                </div>
                <div class="print-info">
                    طُبع في: {current_time} | رقم السند: {payment_id}
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    # حفظ الملف وفتحه
    project_folder_path = os.path.join(documents_folder, "Reports")
    if not os.path.exists(project_folder_path):
        os.makedirs(project_folder_path)

    html_file_path = os.path.join(project_folder_path, f"سند_قبض_{payment_number}_{payment_id}.html")

    try:
        with open(html_file_path, "w", encoding="utf-8") as file:
            file.write(html_content)

        # فتح نافذة الطباعة
        from الطباعة import create_window
        create_window(dialog_instance, html_file_path)

        # إظهار رسالة نجاح
        QMessageBox.information(dialog_instance, "نجاح", f"تم إنشاء سند القبض رقم {payment_number} بنجاح وفتح نافذة الطباعة.")

    except Exception as e:
        QMessageBox.critical(dialog_instance, "خطأ", f"فشل في إنشاء ملف سند القبض:\n{str(e)}")
# 