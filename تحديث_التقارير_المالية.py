"""
تحديث التقارير المالية - منظومة المهندس
إضافة ميزات جديدة للتقارير التفصيلية والطباعة والتصدير
"""

from الإعدادات_العامة import *
from ستايل import *

def show_update_message():
    """عرض رسالة التحديث للمستخدم"""
    msg = QMessageBox()
    msg.setWindowTitle("تحديث التقارير المالية")
    msg.setIcon(QMessageBox.Information)
    msg.setLayoutDirection(Qt.RightToLeft)
    
    msg.setText("""
    🎉 تم تطوير التقارير المالية بنجاح! 🎉
    
    الميزات الجديدة المضافة:
    
    📊 تقارير تفصيلية جديدة:
    • تقرير الأرباح والخسائر
    • تقرير الميزانية العمومية  
    • تقرير تحليل الأداء
    • تقرير المصروفات التفصيلي
    • تقرير العقارات المالي
    • تقرير التدريب المالي
    
    🔧 فلاتر متقدمة:
    • فلترة حسب الفترة الزمنية
    • فلترة حسب الحالة
    • فلاتر ديناميكية لكل تقرير
    
    📈 إحصائيات ديناميكية:
    • إحصائيات تتغير حسب نوع التقرير
    • مؤشرات أداء متقدمة
    • تحليل مالي شامل
    
    🖨️ طباعة محسنة:
    • تصميم احترافي للطباعة
    • دعم A4 مع هوامش مناسبة
    • تضمين الإحصائيات والرسوم البيانية
    
    📤 تصدير متقدم:
    • Excel مع تنسيق احترافي
    • PDF عالي الجودة
    • Word للتحرير
    • CSV للبيانات الخام
    • خيارات تصدير متقدمة
    
    ✨ تحسينات أخرى:
    • واجهة مستخدم محسنة
    • أداء أفضل
    • معالجة أخطاء محسنة
    • دعم كامل للعربية
    """)
    
    msg.setStandardButtons(QMessageBox.Ok)
    msg.exec()

def install_required_packages():
    """تثبيت المكتبات المطلوبة للتقارير المتقدمة"""
    try:
        import subprocess
        import sys
        
        packages = [
            'openpyxl',      # لتصدير Excel المتقدم
            'python-docx',   # لتصدير Word
            'weasyprint',    # لتصدير PDF المتقدم
            'matplotlib',    # للرسوم البيانية
            'seaborn',       # للرسوم البيانية المتقدمة
            'pandas',        # لمعالجة البيانات
            'numpy'          # للحسابات الرياضية
        ]
        
        missing_packages = []
        
        for package in packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            msg = QMessageBox()
            msg.setWindowTitle("تثبيت المكتبات المطلوبة")
            msg.setIcon(QMessageBox.Question)
            msg.setLayoutDirection(Qt.RightToLeft)
            
            msg.setText(f"""
            للاستفادة من جميع ميزات التقارير المتقدمة، يُنصح بتثبيت المكتبات التالية:
            
            {chr(10).join(['• ' + pkg for pkg in missing_packages])}
            
            هل تريد تثبيتها الآن؟
            
            ملاحظة: قد يستغرق التثبيت بضع دقائق
            """)
            
            msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            
            if msg.exec() == QMessageBox.Yes:
                progress = QProgressDialog("جاري تثبيت المكتبات...", "إلغاء", 0, len(missing_packages))
                progress.setWindowTitle("تثبيت المكتبات")
                progress.setLayoutDirection(Qt.RightToLeft)
                progress.show()
                
                for i, package in enumerate(missing_packages):
                    if progress.wasCanceled():
                        break
                    
                    progress.setLabelText(f"تثبيت {package}...")
                    progress.setValue(i)
                    QApplication.processEvents()
                    
                    try:
                        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    except subprocess.CalledProcessError:
                        QMessageBox.warning(None, "تحذير", f"فشل في تثبيت {package}")
                
                progress.setValue(len(missing_packages))
                progress.close()
                
                QMessageBox.information(None, "اكتمل", "تم تثبيت المكتبات بنجاح!")
        
    except Exception as e:
        QMessageBox.warning(None, "خطأ", f"حدث خطأ أثناء تثبيت المكتبات:\n{str(e)}")

def create_reports_shortcuts():
    """إنشاء اختصارات للتقارير المالية"""
    try:
        # يمكن إضافة اختصارات سطح المكتب هنا إذا لزم الأمر
        pass
    except Exception as e:
        print(f"خطأ في إنشاء الاختصارات: {e}")

def update_database_for_reports():
    """تحديث قاعدة البيانات لدعم التقارير الجديدة"""
    try:
        # يمكن إضافة تحديثات قاعدة البيانات هنا إذا لزم الأمر
        # مثل إضافة فهارس جديدة أو تحسين الاستعلامات
        pass
    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية للتحديث"""
    try:
        # عرض رسالة التحديث
        show_update_message()
        
        # تثبيت المكتبات المطلوبة
        install_required_packages()
        
        # إنشاء الاختصارات
        create_reports_shortcuts()
        
        # تحديث قاعدة البيانات
        update_database_for_reports()
        
        # رسالة نجاح التحديث
        QMessageBox.information(None, "تم التحديث", 
            "تم تحديث التقارير المالية بنجاح!\n\n"
            "يمكنك الآن الاستفادة من جميع الميزات الجديدة.")
        
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء التحديث:\n{str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    main()
    app.quit()
