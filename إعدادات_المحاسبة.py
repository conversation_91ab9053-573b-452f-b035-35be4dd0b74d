#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعدادات النظام المحاسبي
"""

import sys
import os
from datetime import datetime, date
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QPushButton, QLabel, 
    QLineEdit, QDateEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QFileDialog,
    QTabWidget, QWidget, QGroupBox, QCheckBox, QProgressBar
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont
import mysql.connector

# استيراد الدوال المساعدة
try:
    from الدوال_الأساسية import *
    from قاعدة_البيانات import *
    from ستايل import *
    from تكامل_المحاسبة import AccountingIntegration
except ImportError:
    print("تعذر استيراد الوحدات المطلوبة")

class AccountingSettingsDialog(QDialog):
    """حوار إعدادات النظام المحاسبي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.accounting = AccountingIntegration()
        
        self.setup_dialog()
        self.create_ui()
        self.load_settings()
    
    def setup_dialog(self):
        """إعداد الحوار"""
        self.setWindowTitle("إعدادات النظام المحاسبي")
        self.setGeometry(200, 200, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # تبويبات الإعدادات
        self.tabs = QTabWidget()
        
        # تبويب الحسابات الافتراضية
        self.default_accounts_tab = QWidget()
        self.create_default_accounts_tab()
        self.tabs.addTab(self.default_accounts_tab, "الحسابات الافتراضية")
        
        # تبويب إعدادات القيود
        self.entries_settings_tab = QWidget()
        self.create_entries_settings_tab()
        self.tabs.addTab(self.entries_settings_tab, "إعدادات القيود")
        
        # تبويب إعدادات التقارير
        self.reports_settings_tab = QWidget()
        self.create_reports_settings_tab()
        self.tabs.addTab(self.reports_settings_tab, "إعدادات التقارير")
        
        # تبويب النسخ الاحتياطي
        self.backup_tab = QWidget()
        self.create_backup_tab()
        self.tabs.addTab(self.backup_tab, "النسخ الاحتياطي")
        
        layout.addWidget(self.tabs)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الإعدادات")
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)
        
        self.reset_btn = QPushButton("إعادة تعيين")
        self.reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_btn)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def create_default_accounts_tab(self):
        """إنشاء تبويب الحسابات الافتراضية"""
        layout = QVBoxLayout(self.default_accounts_tab)
        
        # مجموعة حسابات الأصول
        assets_group = QGroupBox("حسابات الأصول")
        assets_layout = QFormLayout(assets_group)
        
        self.cash_account_edit = QLineEdit()
        self.cash_account_edit.setPlaceholderText("1.1.1")
        assets_layout.addRow("حساب الصندوق:", self.cash_account_edit)
        
        self.bank_account_edit = QLineEdit()
        self.bank_account_edit.setPlaceholderText("1.1.2")
        assets_layout.addRow("حساب البنك:", self.bank_account_edit)
        
        self.customers_account_edit = QLineEdit()
        self.customers_account_edit.setPlaceholderText("1.2.1")
        assets_layout.addRow("حساب العملاء:", self.customers_account_edit)
        
        self.employees_account_edit = QLineEdit()
        self.employees_account_edit.setPlaceholderText("1.2.2")
        assets_layout.addRow("حساب الموظفين:", self.employees_account_edit)
        
        layout.addWidget(assets_group)
        
        # مجموعة حسابات الخصوم
        liabilities_group = QGroupBox("حسابات الخصوم")
        liabilities_layout = QFormLayout(liabilities_group)
        
        self.suppliers_account_edit = QLineEdit()
        self.suppliers_account_edit.setPlaceholderText("2.1.1")
        liabilities_layout.addRow("حساب الموردين:", self.suppliers_account_edit)
        
        self.employee_dues_account_edit = QLineEdit()
        self.employee_dues_account_edit.setPlaceholderText("2.2.1")
        liabilities_layout.addRow("مستحقات الموظفين:", self.employee_dues_account_edit)
        
        layout.addWidget(liabilities_group)
        
        # مجموعة حسابات الإيرادات
        revenues_group = QGroupBox("حسابات الإيرادات")
        revenues_layout = QFormLayout(revenues_group)
        
        self.project_revenue_account_edit = QLineEdit()
        self.project_revenue_account_edit.setPlaceholderText("3.1.1")
        revenues_layout.addRow("إيرادات المشاريع:", self.project_revenue_account_edit)
        
        self.contract_revenue_account_edit = QLineEdit()
        self.contract_revenue_account_edit.setPlaceholderText("3.1.2")
        revenues_layout.addRow("إيرادات العقود:", self.contract_revenue_account_edit)
        
        self.property_revenue_account_edit = QLineEdit()
        self.property_revenue_account_edit.setPlaceholderText("3.1.3")
        revenues_layout.addRow("إيرادات العقارات:", self.property_revenue_account_edit)
        
        layout.addWidget(revenues_group)
        
        # مجموعة حسابات المصروفات
        expenses_group = QGroupBox("حسابات المصروفات")
        expenses_layout = QFormLayout(expenses_group)
        
        self.operational_expenses_account_edit = QLineEdit()
        self.operational_expenses_account_edit.setPlaceholderText("4.1.1")
        expenses_layout.addRow("المصروفات التشغيلية:", self.operational_expenses_account_edit)
        
        self.salaries_account_edit = QLineEdit()
        self.salaries_account_edit.setPlaceholderText("4.1.2")
        expenses_layout.addRow("الرواتب:", self.salaries_account_edit)
        
        self.project_expenses_account_edit = QLineEdit()
        self.project_expenses_account_edit.setPlaceholderText("4.1.3")
        expenses_layout.addRow("مصروفات المشاريع:", self.project_expenses_account_edit)
        
        self.admin_expenses_account_edit = QLineEdit()
        self.admin_expenses_account_edit.setPlaceholderText("4.1.4")
        expenses_layout.addRow("المصروفات الإدارية:", self.admin_expenses_account_edit)
        
        layout.addWidget(expenses_group)
    
    def create_entries_settings_tab(self):
        """إنشاء تبويب إعدادات القيود"""
        layout = QVBoxLayout(self.entries_settings_tab)
        
        # إعدادات عامة للقيود
        general_group = QGroupBox("الإعدادات العامة")
        general_layout = QFormLayout(general_group)
        
        self.auto_approve_entries = QCheckBox("اعتماد القيود تلقائياً")
        self.auto_approve_entries.setChecked(True)
        general_layout.addRow("الاعتماد التلقائي:", self.auto_approve_entries)
        
        self.require_description = QCheckBox("وصف القيد مطلوب")
        self.require_description.setChecked(True)
        general_layout.addRow("وصف القيد:", self.require_description)
        
        self.allow_negative_amounts = QCheckBox("السماح بالمبالغ السالبة")
        general_layout.addRow("المبالغ السالبة:", self.allow_negative_amounts)
        
        layout.addWidget(general_group)
        
        # إعدادات ترقيم القيود
        numbering_group = QGroupBox("ترقيم القيود")
        numbering_layout = QFormLayout(numbering_group)
        
        self.entry_prefix_edit = QLineEdit()
        self.entry_prefix_edit.setText("عام")
        self.entry_prefix_edit.setPlaceholderText("عام")
        numbering_layout.addRow("بادئة رقم القيد:", self.entry_prefix_edit)
        
        self.reset_numbering_yearly = QCheckBox("إعادة تعيين الترقيم سنوياً")
        self.reset_numbering_yearly.setChecked(True)
        numbering_layout.addRow("إعادة التعيين السنوي:", self.reset_numbering_yearly)
        
        layout.addWidget(numbering_group)
        
        # إعدادات التحقق
        validation_group = QGroupBox("التحقق من صحة البيانات")
        validation_layout = QFormLayout(validation_group)
        
        self.check_balance = QCheckBox("التحقق من توازن القيد")
        self.check_balance.setChecked(True)
        validation_layout.addRow("توازن القيد:", self.check_balance)
        
        self.check_account_exists = QCheckBox("التحقق من وجود الحساب")
        self.check_account_exists.setChecked(True)
        validation_layout.addRow("وجود الحساب:", self.check_account_exists)
        
        self.tolerance_amount_edit = QDoubleSpinBox()
        self.tolerance_amount_edit.setMaximum(10.0)
        self.tolerance_amount_edit.setValue(0.01)
        self.tolerance_amount_edit.setSuffix(" ريال")
        validation_layout.addRow("هامش التسامح:", self.tolerance_amount_edit)
        
        layout.addWidget(validation_group)
    
    def create_reports_settings_tab(self):
        """إنشاء تبويب إعدادات التقارير"""
        layout = QVBoxLayout(self.reports_settings_tab)
        
        # إعدادات عرض التقارير
        display_group = QGroupBox("إعدادات العرض")
        display_layout = QFormLayout(display_group)
        
        self.show_zero_balances_reports = QCheckBox("إظهار الأرصدة الصفرية")
        display_layout.addRow("الأرصدة الصفرية:", self.show_zero_balances_reports)
        
        self.group_by_account_type = QCheckBox("تجميع حسب نوع الحساب")
        self.group_by_account_type.setChecked(True)
        display_layout.addRow("التجميع حسب النوع:", self.group_by_account_type)
        
        self.decimal_places_spin = QSpinBox()
        self.decimal_places_spin.setRange(0, 4)
        self.decimal_places_spin.setValue(2)
        display_layout.addRow("عدد المنازل العشرية:", self.decimal_places_spin)
        
        layout.addWidget(display_group)
        
        # إعدادات الطباعة
        print_group = QGroupBox("إعدادات الطباعة")
        print_layout = QFormLayout(print_group)
        
        self.company_name_edit = QLineEdit()
        self.company_name_edit.setPlaceholderText("اسم الشركة")
        print_layout.addRow("اسم الشركة:", self.company_name_edit)
        
        self.company_address_edit = QTextEdit()
        self.company_address_edit.setMaximumHeight(60)
        self.company_address_edit.setPlaceholderText("عنوان الشركة")
        print_layout.addRow("عنوان الشركة:", self.company_address_edit)
        
        self.include_logo = QCheckBox("تضمين شعار الشركة")
        print_layout.addRow("الشعار:", self.include_logo)
        
        layout.addWidget(print_group)
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        layout = QVBoxLayout(self.backup_tab)
        
        # إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_layout = QFormLayout(backup_group)
        
        self.auto_backup = QCheckBox("نسخ احتياطي تلقائي")
        backup_layout.addRow("النسخ التلقائي:", self.auto_backup)
        
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems(["يومي", "أسبوعي", "شهري"])
        backup_layout.addRow("تكرار النسخ:", self.backup_frequency_combo)
        
        self.backup_path_edit = QLineEdit()
        self.backup_path_edit.setPlaceholderText("مسار حفظ النسخ الاحتياطية")
        backup_layout.addRow("مسار الحفظ:", self.backup_path_edit)
        
        self.browse_backup_btn = QPushButton("تصفح...")
        self.browse_backup_btn.clicked.connect(self.browse_backup_path)
        backup_layout.addRow("", self.browse_backup_btn)
        
        layout.addWidget(backup_group)
        
        # إجراءات النسخ الاحتياطي
        actions_group = QGroupBox("إجراءات النسخ الاحتياطي")
        actions_layout = QVBoxLayout(actions_group)
        
        self.create_backup_btn = QPushButton("إنشاء نسخة احتياطية الآن")
        self.create_backup_btn.clicked.connect(self.create_backup)
        actions_layout.addWidget(self.create_backup_btn)
        
        self.restore_backup_btn = QPushButton("استعادة من نسخة احتياطية")
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        actions_layout.addWidget(self.restore_backup_btn)
        
        layout.addWidget(actions_group)
    
    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if not self.accounting.get_connection():
                return
            
            # تحميل الحسابات الافتراضية
            for account_type, default_code in self.accounting.default_accounts.items():
                if account_type == 'صندوق':
                    self.cash_account_edit.setText(default_code)
                elif account_type == 'بنك':
                    self.bank_account_edit.setText(default_code)
                elif account_type == 'عملاء':
                    self.customers_account_edit.setText(default_code)
                elif account_type == 'موظفين':
                    self.employees_account_edit.setText(default_code)
                elif account_type == 'موردين':
                    self.suppliers_account_edit.setText(default_code)
                elif account_type == 'مستحقات_موظفين':
                    self.employee_dues_account_edit.setText(default_code)
                elif account_type == 'ايرادات_مشاريع':
                    self.project_revenue_account_edit.setText(default_code)
                elif account_type == 'ايرادات_عقود':
                    self.contract_revenue_account_edit.setText(default_code)
                elif account_type == 'ايرادات_عقارات':
                    self.property_revenue_account_edit.setText(default_code)
                elif account_type == 'مصروفات_تشغيلية':
                    self.operational_expenses_account_edit.setText(default_code)
                elif account_type == 'رواتب':
                    self.salaries_account_edit.setText(default_code)
                elif account_type == 'مصروفات_مشاريع':
                    self.project_expenses_account_edit.setText(default_code)
                elif account_type == 'مصروفات_ادارية':
                    self.admin_expenses_account_edit.setText(default_code)
            
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تحديث الحسابات الافتراضية
            self.accounting.default_accounts.update({
                'صندوق': self.cash_account_edit.text().strip(),
                'بنك': self.bank_account_edit.text().strip(),
                'عملاء': self.customers_account_edit.text().strip(),
                'موظفين': self.employees_account_edit.text().strip(),
                'موردين': self.suppliers_account_edit.text().strip(),
                'مستحقات_موظفين': self.employee_dues_account_edit.text().strip(),
                'ايرادات_مشاريع': self.project_revenue_account_edit.text().strip(),
                'ايرادات_عقود': self.contract_revenue_account_edit.text().strip(),
                'ايرادات_عقارات': self.property_revenue_account_edit.text().strip(),
                'مصروفات_تشغيلية': self.operational_expenses_account_edit.text().strip(),
                'رواتب': self.salaries_account_edit.text().strip(),
                'مصروفات_مشاريع': self.project_expenses_account_edit.text().strip(),
                'مصروفات_ادارية': self.admin_expenses_account_edit.text().strip()
            })
            
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        reply = QMessageBox.question(self, "تأكيد إعادة التعيين", 
                                    "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.load_settings()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات إلى القيم الافتراضية")
    
    def browse_backup_path(self):
        """تصفح مسار النسخ الاحتياطي"""
        path = QFileDialog.getExistingDirectory(self, "اختر مجلد النسخ الاحتياطي")
        if path:
            self.backup_path_edit.setText(path)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        QMessageBox.information(self, "قريباً", "ميزة النسخ الاحتياطي ستكون متاحة قريباً")
    
    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        QMessageBox.information(self, "قريباً", "ميزة الاستعادة ستكون متاحة قريباً")
    
    def closeEvent(self, event):
        """إغلاق الاتصال عند إغلاق الحوار"""
        self.accounting.close_connection()
        event.accept()
