from الإعدادات_العامة import *
from ستايل import *
from أزرار_الواجهة import *
from قاعدة_البيانات import *
import qtawesome as qta
from datetime import datetime, date
import mysql.connector

class CustodyDialog(QDialog):
    """حوار إضافة/تعديل العهدة المالية"""

    def __init__(self, parent=None, custody_id=None, project_id=None):
        super().__init__(parent)
        self.custody_id = custody_id
        self.project_id = project_id
        self.is_edit_mode = custody_id is not None

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_custody_data()

        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل العهدة المالية" if self.is_edit_mode else "إضافة عهدة مالية جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # معلومات العهدة الأساسية
        form_layout = QFormLayout()

        # رقم العهدة
        self.custody_number_edit = QLineEdit()
        if not self.is_edit_mode:
            self.custody_number_edit.setText(self.generate_custody_number())
        form_layout.addRow("رقم العهدة:", self.custody_number_edit)

        # اختيار المشروع
        self.project_combo = QComboBox()
        self.project_combo.setMinimumWidth(300)
        self.load_projects()
        form_layout.addRow("المشروع:", self.project_combo)

        # مبلغ العهدة
        amount_layout = QVBoxLayout()
        self.amount_edit = QLineEdit()
        # السماح بالأرقام السالبة والموجبة لمبلغ العهدة
        self.amount_edit.setValidator(QDoubleValidator(-999999999.99, 999999999.99, 2))
        self.amount_edit.setToolTip("يمكن إدخال قيم سالبة في حالة العجز أو الديون")
        amount_layout.addWidget(self.amount_edit)

        # تلميح للمستخدم
        amount_hint = QLabel("💡 يمكن إدخال قيم سالبة للعجز أو الديون")
        amount_hint.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        amount_layout.addWidget(amount_hint)

        amount_widget = QWidget()
        amount_widget.setLayout(amount_layout)
        form_layout.addRow("مبلغ العهدة:", amount_widget)

        # نسبة المكتب
        self.office_percentage_edit = QLineEdit()
        self.office_percentage_edit.setValidator(QDoubleValidator(0.00, 100.00, 2))
        self.office_percentage_edit.setText("0")
        form_layout.addRow("نسبة المكتب %:", self.office_percentage_edit)

        # تاريخ العهدة
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ العهدة:", self.date_edit)

        # حالة العهدة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["مفتوحة", "مغلقة", "مرحلة"])
        form_layout.addRow("حالة العهدة:", self.status_combo)

        # مبلغ مرحل من عهدة سابقة
        self.transferred_amount_edit = QLineEdit()
        # السماح بالقيم السالبة والموجبة للمبلغ المرحل
        self.transferred_amount_edit.setValidator(QDoubleValidator(-999999999.99, 999999999.99, 2))
        self.transferred_amount_edit.setText("0")
        self.transferred_amount_edit.setToolTip("يمكن إدخال قيم سالبة في حالة ترحيل عجز من عهدة سابقة")
        form_layout.addRow("مبلغ مرحل من عهدة سابقة:", self.transferred_amount_edit)

        # تلميح للمبلغ المرحل
        transferred_hint = QLabel("💡 القيم السالبة تعني ترحيل عجز من عهدة سابقة")
        transferred_hint.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        transferred_hint.setWordWrap(True)
        form_layout.addRow("", transferred_hint)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # معلومات محسوبة (للعرض فقط)
        info_group = QGroupBox("معلومات محسوبة")
        info_layout = QFormLayout(info_group)

        self.office_amount_label = QLabel("0.00")
        self.office_amount_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        info_layout.addRow("مبلغ نسبة المكتب:", self.office_amount_label)

        self.net_amount_label = QLabel("0.00")
        self.net_amount_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        info_layout.addRow("المبلغ الصافي:", self.net_amount_label)

        layout.addWidget(info_group)

        # ربط تغيير المبلغ ونسبة المكتب بحساب المبالغ
        self.amount_edit.textChanged.connect(self.calculate_amounts)
        self.office_percentage_edit.textChanged.connect(self.calculate_amounts)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton(qta.icon('fa5s.save', color='green'), "حفظ")
        save_btn.setMinimumSize(100, 40)
        save_btn.clicked.connect(self.save_custody)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton(qta.icon('fa5s.times', color='red'), "إلغاء")
        cancel_btn.setMinimumSize(100, 40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

    def load_projects(self):
        """تحميل قائمة المشاريع"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.id, p.اسم_المشروع, c.اسم_العميل
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                ORDER BY p.اسم_المشروع
            """)

            projects = cursor.fetchall()

            for project in projects:
                display_text = f"{project[1]} - {project[2] or 'غير محدد'}"
                self.project_combo.addItem(display_text, project[0])

            # تحديد المشروع المحدد مسبقاً
            if self.project_id:
                for i in range(self.project_combo.count()):
                    if self.project_combo.itemData(i) == self.project_id:
                        self.project_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المشاريع: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def generate_custody_number(self):
        """توليد رقم عهدة تلقائي"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # الحصول على آخر رقم عهدة
            cursor.execute("SELECT COUNT(*) FROM المقاولات_العهد")
            count = cursor.fetchone()[0]

            # توليد رقم جديد
            current_year = datetime.now().year
            new_number = f"C{current_year}{count + 1:04d}"

            return new_number

        except Exception as e:
            return f"C{datetime.now().year}001"
        finally:
            if 'conn' in locals():
                conn.close()

    def calculate_amounts(self):
        """حساب المبالغ المحسوبة"""
        try:
            amount = float(self.amount_edit.text() or 0)
            percentage = float(self.office_percentage_edit.text() or 0)

            office_amount = amount * percentage / 100
            net_amount = amount - office_amount

            self.office_amount_label.setText(f"{office_amount:,.2f}")
            self.net_amount_label.setText(f"{net_amount:,.2f}")

        except ValueError:
            self.office_amount_label.setText("0.00")
            self.net_amount_label.setText("0.00")

    def load_custody_data(self):
        """تحميل بيانات العهدة للتعديل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT رقم_العهدة, معرف_المشروع, مبلغ_العهدة, نسبة_المكتب,
                       تاريخ_العهدة, حالة_العهدة, مبلغ_مرحل_من_السابقة, ملاحظات
                FROM المقاولات_العهد
                WHERE id = %s
            """, (self.custody_id,))

            data = cursor.fetchone()
            if data:
                self.custody_number_edit.setText(data[0] or "")

                # تحديد المشروع
                for i in range(self.project_combo.count()):
                    if self.project_combo.itemData(i) == data[1]:
                        self.project_combo.setCurrentIndex(i)
                        break

                self.amount_edit.setText(str(data[2] or 0))
                self.office_percentage_edit.setText(str(data[3] or 0))

                if data[4]:
                    self.date_edit.setDate(QDate.fromString(str(data[4]), "yyyy-MM-dd"))

                status_index = self.status_combo.findText(data[5] or "مفتوحة")
                if status_index >= 0:
                    self.status_combo.setCurrentIndex(status_index)

                self.transferred_amount_edit.setText(str(data[6] or 0))
                self.notes_edit.setPlainText(data[7] or "")

                # حساب المبالغ
                self.calculate_amounts()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات العهدة: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def save_custody(self):
        """حفظ العهدة"""
        # التحقق من صحة البيانات
        if not self.custody_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم العهدة")
            return

        if self.project_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار المشروع")
            return

        if not self.amount_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ العهدة")
            return

        # التحقق من القيم السالبة وتحذير المستخدم
        amount_value = float(self.amount_edit.text())
        if amount_value < 0:
            reply = QMessageBox.question(self, "تأكيد القيمة السالبة",
                                       f"مبلغ العهدة سالب ({amount_value:,.2f}).\n"
                                       "هذا يعني أن العهدة تمثل عجز أو دين.\n"
                                       "هل تريد المتابعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # الحصول على معلومات المشروع والعميل
            project_id = self.project_combo.currentData()
            cursor.execute("""
                SELECT p.اسم_المشروع, c.اسم_العميل
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                WHERE p.id = %s
            """, (project_id,))

            project_info = cursor.fetchone()
            project_name = project_info[0] if project_info else ""
            client_name = project_info[1] if project_info else ""

            # بيانات العهدة للتحديث
            custody_data_update = (
                self.custody_number_edit.text().strip(),
                project_id,
                project_name,
                client_name,
                float(self.amount_edit.text()),
                float(self.office_percentage_edit.text() or 0),
                self.date_edit.date().toString("yyyy-MM-dd"),
                self.status_combo.currentText(),
                float(self.transferred_amount_edit.text() or 0),
                self.notes_edit.toPlainText(),
                datetime.now().year
            )

            # بيانات العهدة للإدراج
            custody_data_insert = (
                self.custody_number_edit.text().strip(),
                project_id,
                project_name,
                client_name,
                float(self.amount_edit.text()),
                float(self.office_percentage_edit.text() or 0),
                self.date_edit.date().toString("yyyy-MM-dd"),
                self.status_combo.currentText(),
                float(self.transferred_amount_edit.text() or 0),
                self.notes_edit.toPlainText(),
                "admin",  # المستخدم
                datetime.now().year  # السنة
            )

            if self.is_edit_mode:
                # تحديث العهدة
                cursor.execute("""
                    UPDATE المقاولات_العهد SET
                        رقم_العهدة = %s, معرف_المشروع = %s, اسم_المشروع = %s, اسم_العميل = %s,
                        مبلغ_العهدة = %s, نسبة_المكتب = %s, تاريخ_العهدة = %s, حالة_العهدة = %s,
                        مبلغ_مرحل_من_السابقة = %s, ملاحظات = %s, السنة = %s
                    WHERE id = %s
                """, custody_data_update + (self.custody_id,))
            else:
                # إضافة عهدة جديدة
                cursor.execute("""
                    INSERT INTO المقاولات_العهد
                    (رقم_العهدة, معرف_المشروع, اسم_المشروع, اسم_العميل, مبلغ_العهدة,
                     نسبة_المكتب, تاريخ_العهدة, حالة_العهدة, مبلغ_مرحل_من_السابقة,
                     ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, custody_data_insert)

                # الحصول على id العهدة الجديدة
                new_custody_id = cursor.lastrowid

                # إضافة دفعة تلقائية إذا كان مبلغ العهدة أكبر من صفر
                custody_amount = float(self.amount_edit.text())
                if custody_amount != 0:
                    payment_description = "الدفعة الأولى للعهدة"
                    payment_type = "دفعة_أولى"

                    cursor.execute("""
                        INSERT INTO دفعات_العهد
                        (معرف_العهدة, رقم_العهدة, وصف_الدفعة, المبلغ, تاريخ_الدفعة,
                         نوع_الدفعة, طريقة_الدفع, المستلم, ملاحظات, المستخدم, السنة)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        new_custody_id,
                        self.custody_number_edit.text().strip(),
                        payment_description,
                        custody_amount,
                        self.date_edit.date().toString("yyyy-MM-dd"),
                        payment_type,
                        "نقدي",  # طريقة دفع افتراضية
                        "",  # المستلم
                        "دفعة تلقائية عند إنشاء العهدة",
                        "admin",
                        datetime.now().year
                    ))

            conn.commit()

            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} العهدة بنجاح")
            self.accept()

        except mysql.connector.IntegrityError:
            QMessageBox.warning(self, "خطأ", "رقم العهدة موجود بالفعل")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ العهدة: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()


class ExpenseDialog(QDialog):
    """حوار إضافة/تعديل مصروف العهدة"""

    def __init__(self, parent=None, custody_id=None, expense_id=None):
        super().__init__(parent)
        self.custody_id = custody_id
        self.expense_id = expense_id
        self.is_edit_mode = expense_id is not None

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_expense_data()

        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل المصروف" if self.is_edit_mode else "إضافة مصروف جديد"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # نموذج المصروف
        form_layout = QFormLayout()

        # نوع المصروف
        self.type_combo = QComboBox()
        self.type_combo.setEditable(True)
        self.type_combo.addItems([
             "مرتبط بعهدة", "غير مرتبط بعهدة"
        ])
        form_layout.addRow("نوع المصروف:", self.type_combo)

        # رقم العهدة
        self. custody_combo = QComboBox()       
        form_layout.addRow("رقم العهدة:", self.custody_combo)

        # فئة المصروف
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.addItems([
            "","مواد بناء", "عمالة", "مقاول", "معدات", "نقل", "استشارات",
            "رسوم حكومية", "مصاريف إدارية"
        ])
        form_layout.addRow("تصنيف المصروف:", self.category_combo)

        # وصف المصروف
        self.description_edit = QLineEdit()
        form_layout.addRow("وصف المصروف:", self.description_edit)

        # المبلغ
        self.amount_edit = QLineEdit()
        # السماح بالأرقام السالبة والموجبة للمصروفات
        self.amount_edit.setValidator(QDoubleValidator(-999999999.99, 999999999.99, 2))
        self.amount_edit.setToolTip("يمكن إدخال قيم سالبة للمردودات أو التصحيحات")
        form_layout.addRow("المبلغ:", self.amount_edit)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([
            "نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"
        ])
        form_layout.addRow("طريقة الدفع:", self.payment_method_combo)

        # تاريخ المصروف
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ المصروف:", self.date_edit)

        # المستلم
        self.recipient_edit = QLineEdit()
        form_layout.addRow("المستلم:", self.recipient_edit)

        # رقم الفاتورة
        self.invoice_number_edit = QLineEdit()
        form_layout.addRow("رقم الفاتورة:", self.invoice_number_edit)

        # المورد
        self.supplier_edit = QLineEdit()
        form_layout.addRow("المورد:", self.supplier_edit)

        # ملاحظات
        self.notes_edit = QLineEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton(qta.icon('fa5s.save', color='green'), "حفظ")
        save_btn.setMinimumSize(100, 40)
        save_btn.clicked.connect(self.save_expense)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton(qta.icon('fa5s.times', color='red'), "إلغاء")
        cancel_btn.setMinimumSize(100, 40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

    def load_expense_data(self):
        """تحميل بيانات المصروف للتعديل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT وصف_المصروف, المبلغ, تاريخ_المصروف, المستلم, طريقة_الدفع,
                       رقم_الفاتورة, المورد, فئة_المصروف, ملاحظات
                FROM مصروفات_العهد
                WHERE id = %s
            """, (self.expense_id,))

            data = cursor.fetchone()
            if data:
                self.description_edit.setText(data[0] or "")
                self.amount_edit.setText(str(data[1] or 0))

                if data[2]:
                    self.date_edit.setDate(QDate.fromString(str(data[2]), "yyyy-MM-dd"))

                self.recipient_edit.setText(data[3] or "")

                method_index = self.payment_method_combo.findText(data[4] or "نقدي")
                if method_index >= 0:
                    self.payment_method_combo.setCurrentIndex(method_index)

                self.invoice_number_edit.setText(data[5] or "")
                self.supplier_edit.setText(data[6] or "")
                self.category_combo.setCurrentText(data[7] or "")
                self.notes_edit.setPlainText(data[8] or "")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات المصروف: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def save_expense(self):
        """حفظ المصروف"""
        # التحقق من صحة البيانات
        if not self.description_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال وصف المصروف")
            return

        if not self.amount_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ المصروف")
            return

        try:
            amount_value = float(self.amount_edit.text())
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # تأكيد للقيم السالبة
        if amount_value < 0:
            reply = QMessageBox.question(self, "تأكيد المبلغ السالب",
                                       f"المبلغ المدخل سالب ({amount_value:,.2f}).\n"
                                       "هذا يعني مردود أو تصحيح للمصروفات.\n"
                                       "هل تريد المتابعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # الحصول على رقم العهدة
            cursor.execute("SELECT رقم_العهدة FROM المقاولات_العهد WHERE id = %s", (self.custody_id,))
            custody_number = cursor.fetchone()[0]

            # بيانات المصروف للتحديث
            expense_data_update = (
                self.custody_id,
                custody_number,
                self.description_edit.text().strip(),
                float(self.amount_edit.text()),
                self.date_edit.date().toString("yyyy-MM-dd"),
                self.recipient_edit.text().strip(),
                self.payment_method_combo.currentText(),
                self.invoice_number_edit.text().strip(),
                self.supplier_edit.text().strip(),
                self.category_combo.currentText(),
                self.notes_edit.toPlainText(),
                datetime.now().year
            )

            # بيانات المصروف للإدراج
            expense_data_insert = (
                self.custody_id,
                custody_number,
                self.description_edit.text().strip(),
                float(self.amount_edit.text()),
                self.date_edit.date().toString("yyyy-MM-dd"),
                self.recipient_edit.text().strip(),
                self.payment_method_combo.currentText(),
                self.invoice_number_edit.text().strip(),
                self.supplier_edit.text().strip(),
                self.category_combo.currentText(),
                self.notes_edit.toPlainText(),
                "admin",  # المستخدم
                datetime.now().year  # السنة
            )

            if self.is_edit_mode:
                # تحديث المصروف
                cursor.execute("""
                    UPDATE مصروفات_العهد SET
                        معرف_العهدة = %s, رقم_العهدة = %s, وصف_المصروف = %s, المبلغ = %s,
                        تاريخ_المصروف = %s, المستلم = %s, طريقة_الدفع = %s, رقم_الفاتورة = %s,
                        المورد = %s, فئة_المصروف = %s, ملاحظات = %s, السنة = %s
                    WHERE id = %s
                """, expense_data_update + (self.expense_id,))
            else:
                # إضافة مصروف جديد
                cursor.execute("""
                    INSERT INTO مصروفات_العهد
                    (معرف_العهدة, رقم_العهدة, وصف_المصروف, المبلغ, تاريخ_المصروف,
                     المستلم, طريقة_الدفع, رقم_الفاتورة, المورد, فئة_المصروف,
                     ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, expense_data_insert)

            conn.commit()

            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} المصروف بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ المصروف: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()


class TransferCustodyDialog(QDialog):
    """حوار ترحيل العهدة إلى عهدة جديدة"""

    def __init__(self, parent=None, source_custody_id=None, remaining_amount=0):
        super().__init__(parent)
        self.source_custody_id = source_custody_id
        self.remaining_amount = remaining_amount

        self.setup_dialog()
        self.create_ui()
        self.load_source_custody_info()

        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        self.setWindowTitle("ترحيل العهدة إلى عهدة جديدة")
        self.setGeometry(200, 200, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # معلومات العهدة المصدر
        source_group = QGroupBox("معلومات العهدة المصدر")
        source_layout = QFormLayout(source_group)

        self.source_info_label = QLabel()
        self.source_info_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        source_layout.addRow("العهدة المصدر:", self.source_info_label)

        self.remaining_label = QLabel(f"{self.remaining_amount:,.2f}")
        # تغيير اللون حسب القيمة (أخضر للموجب، أحمر للسالب)
        color = "#27ae60" if self.remaining_amount >= 0 else "#e74c3c"
        self.remaining_label.setStyleSheet(f"font-weight: bold; color: {color}; font-size: 14px;")
        source_layout.addRow("المبلغ المتبقي:", self.remaining_label)

        layout.addWidget(source_group)

        # معلومات العهدة الجديدة
        new_group = QGroupBox("معلومات العهدة الجديدة")
        new_layout = QFormLayout(new_group)

        # رقم العهدة الجديدة
        self.new_custody_number_edit = QLineEdit()
        self.new_custody_number_edit.setText(self.generate_custody_number())
        new_layout.addRow("رقم العهدة الجديدة:", self.new_custody_number_edit)

        # المبلغ المرحل
        self.transfer_amount_edit = QLineEdit()
        # السماح بالقيم السالبة والموجبة
        self.transfer_amount_edit.setValidator(QDoubleValidator(-999999999.99, 999999999.99, 2))
        self.transfer_amount_edit.setText(str(self.remaining_amount))
        self.transfer_amount_edit.setToolTip("يمكن إدخال قيم سالبة لترحيل العجز أو الدين")
        new_layout.addRow("المبلغ المرحل:", self.transfer_amount_edit)

        # تلميح للمستخدم
        transfer_hint = QLabel("💡 يمكن إدخال قيم سالبة لترحيل العجز أو الدين إلى العهدة الجديدة")
        transfer_hint.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        transfer_hint.setWordWrap(True)
        new_layout.addRow("", transfer_hint)

        # مبلغ إضافي للعهدة الجديدة
        additional_layout = QVBoxLayout()
        self.additional_amount_edit = QLineEdit()
        # السماح بالأرقام السالبة والموجبة للمبلغ الإضافي
        self.additional_amount_edit.setValidator(QDoubleValidator(-999999999.99, 999999999.99, 2))
        self.additional_amount_edit.setText("0")
        self.additional_amount_edit.setToolTip("يمكن إدخال قيم سالبة لتقليل مبلغ العهدة الجديدة")
        additional_layout.addWidget(self.additional_amount_edit)

        # تلميح للمبلغ الإضافي
        additional_hint = QLabel("💡 يمكن إدخال قيم سالبة لتقليل المبلغ")
        additional_hint.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        additional_layout.addWidget(additional_hint)

        additional_widget = QWidget()
        additional_widget.setLayout(additional_layout)
        new_layout.addRow("مبلغ إضافي:", additional_widget)

        # نسبة المكتب للعهدة الجديدة
        self.new_office_percentage_edit = QLineEdit()
        self.new_office_percentage_edit.setValidator(QDoubleValidator(0.00, 100.00, 2))
        self.new_office_percentage_edit.setText("0")
        new_layout.addRow("نسبة المكتب %:", self.new_office_percentage_edit)

        # تاريخ العهدة الجديدة
        self.new_date_edit = QDateEdit()
        self.new_date_edit.setDate(QDate.currentDate())
        self.new_date_edit.setCalendarPopup(True)
        new_layout.addRow("تاريخ العهدة الجديدة:", self.new_date_edit)

        # ملاحظات الترحيل
        self.transfer_notes_edit = QTextEdit()
        self.transfer_notes_edit.setMaximumHeight(80)
        new_layout.addRow("ملاحظات الترحيل:", self.transfer_notes_edit)

        layout.addWidget(new_group)

        # معلومات محسوبة
        calc_group = QGroupBox("معلومات محسوبة")
        calc_layout = QFormLayout(calc_group)

        self.total_new_amount_label = QLabel("0.00")
        self.total_new_amount_label.setStyleSheet("font-weight: bold; color: #3498db;")
        calc_layout.addRow("إجمالي مبلغ العهدة الجديدة:", self.total_new_amount_label)

        layout.addWidget(calc_group)

        # ربط تغيير المبالغ بحساب الإجمالي
        self.transfer_amount_edit.textChanged.connect(self.calculate_total)
        self.additional_amount_edit.textChanged.connect(self.calculate_total)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        transfer_btn = QPushButton(qta.icon('fa5s.exchange-alt', color='blue'), "ترحيل")
        transfer_btn.setMinimumSize(120, 40)
        transfer_btn.clicked.connect(self.transfer_custody)
        buttons_layout.addWidget(transfer_btn)

        cancel_btn = QPushButton(qta.icon('fa5s.times', color='red'), "إلغاء")
        cancel_btn.setMinimumSize(100, 40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # حساب الإجمالي الأولي
        self.calculate_total()

    def load_source_custody_info(self):
        """تحميل معلومات العهدة المصدر"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT رقم_العهدة, اسم_المشروع, اسم_العميل
                FROM المقاولات_العهد
                WHERE id = %s
            """, (self.source_custody_id,))

            data = cursor.fetchone()
            if data:
                info_text = f"العهدة: {data[0]} - المشروع: {data[1]} - العميل: {data[2]}"
                self.source_info_label.setText(info_text)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل معلومات العهدة: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def generate_custody_number(self):
        """توليد رقم عهدة جديد"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM المقاولات_العهد")
            count = cursor.fetchone()[0]

            current_year = datetime.now().year
            new_number = f"C{current_year}{count + 1:04d}"

            return new_number

        except Exception as e:
            return f"C{datetime.now().year}001"
        finally:
            if 'conn' in locals():
                conn.close()

    def calculate_total(self):
        """حساب إجمالي مبلغ العهدة الجديدة"""
        try:
            transfer_amount = float(self.transfer_amount_edit.text() or 0)
            additional_amount = float(self.additional_amount_edit.text() or 0)

            total = transfer_amount + additional_amount

            # تغيير اللون حسب القيمة (أخضر للموجب، أحمر للسالب، أزرق للصفر)
            if total > 0:
                color = "#27ae60"  # أخضر
                status = "موجب"
            elif total < 0:
                color = "#e74c3c"  # أحمر
                status = "سالب (عجز)"
            else:
                color = "#3498db"  # أزرق
                status = "صفر"

            self.total_new_amount_label.setText(f"{total:,.2f}")
            self.total_new_amount_label.setStyleSheet(f"font-weight: bold; color: {color};")

            # إضافة تلميح للحالة
            self.total_new_amount_label.setToolTip(f"حالة العهدة الجديدة: {status}")

        except ValueError:
            self.total_new_amount_label.setText("0.00")
            self.total_new_amount_label.setStyleSheet("font-weight: bold; color: #3498db;")

    def transfer_custody(self):
        """تنفيذ عملية الترحيل"""
        # التحقق من صحة البيانات
        if not self.new_custody_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم العهدة الجديدة")
            return

        transfer_amount = float(self.transfer_amount_edit.text() or 0)

        # السماح بالقيم السالبة والموجبة
        if transfer_amount == 0:
            reply = QMessageBox.question(self, "تأكيد",
                                       "مبلغ الترحيل صفر. هل تريد المتابعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return

        # التحقق من إجمالي مبلغ العهدة الجديدة
        additional_amount = float(self.additional_amount_edit.text() or 0)
        total_new_amount = transfer_amount + additional_amount

        # تحذير للقيم السالبة في المبلغ الإجمالي
        if total_new_amount < 0:
            reply = QMessageBox.question(self, "تأكيد إنشاء عهدة بعجز",
                                       f"إجمالي مبلغ العهدة الجديدة سالب ({total_new_amount:,.2f}).\n"
                                       "هذا يعني أن العهدة الجديدة ستبدأ بعجز.\n"
                                       "هل تريد المتابعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return
        elif transfer_amount < 0:
            reply = QMessageBox.question(self, "تأكيد ترحيل العجز",
                                       f"سيتم ترحيل عجز بمبلغ {abs(transfer_amount):,.2f}\n"
                                       "هل تريد المتابعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # الحصول على معلومات العهدة المصدر
            cursor.execute("""
                SELECT معرف_المشروع, اسم_المشروع, اسم_العميل
                FROM المقاولات_العهد
                WHERE id = %s
            """, (self.source_custody_id,))

            source_data = cursor.fetchone()

            # استخدام المبلغ الإجمالي المحسوب مسبقاً
            office_percentage = float(self.new_office_percentage_edit.text() or 0)

            # إنشاء العهدة الجديدة
            cursor.execute("""
                INSERT INTO المقاولات_العهد
                (رقم_العهدة, معرف_المشروع, اسم_المشروع, اسم_العميل, مبلغ_العهدة,
                 نسبة_المكتب, تاريخ_العهدة, حالة_العهدة, معرف_العهدة_السابقة,
                 مبلغ_مرحل_من_السابقة, ملاحظات, المستخدم, السنة)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                self.new_custody_number_edit.text().strip(),
                source_data[0],  # معرف_المشروع
                source_data[1],  # اسم_المشروع
                source_data[2],  # اسم_العميل
                total_new_amount,
                office_percentage,
                self.new_date_edit.date().toString("yyyy-MM-dd"),
                "مفتوحة",
                self.source_custody_id,
                transfer_amount,
                self.transfer_notes_edit.toPlainText(),
                "admin",
                datetime.now().year
            ))

            # الحصول على id العهدة الجديدة
            new_custody_id = cursor.lastrowid

            # ===== الربط المحاسبي للعهدة الجديدة =====
            try:
                # إنشاء نظام الربط المحاسبي
                from تكامل_المحاسبة import AccountingIntegration
                accounting = AccountingIntegration()

                # إعداد بيانات العهدة للنظام المحاسبي
                custody_data = {
                    'id': new_custody_id,
                    'رقم_العهدة': self.new_custody_number_edit.text().strip(),
                    'معرف_المشروع': source_data[0],
                    'اسم_المشروع': source_data[1],
                    'اسم_العميل': source_data[2],
                    'مبلغ_العهدة': total_new_amount,
                    'نسبة_المكتب': office_percentage,
                    'تاريخ_الإنشاء': self.new_date_edit.date().toString("yyyy-MM-dd"),
                    'المستخدم': 'admin'
                }

                # تسجيل العهدة محاسبياً
                success, message = accounting.record_custody_transaction(custody_data)

                if success:
                    print(f"تم تسجيل العهدة محاسبياً: {message}")
                else:
                    print(f"خطأ في التسجيل المحاسبي: {message}")

                accounting.close_connection()

            except Exception as e:
                print(f"خطأ في الربط المحاسبي للعهدة: {e}")
                # الاستمرار حتى لو فشل الربط المحاسبي
            # ===== نهاية الربط المحاسبي =====

            # إضافة دفعة المبلغ المرحل إذا كان أكبر من صفر
            if transfer_amount != 0:
                # الحصول على رقم العهدة المصدر
                cursor.execute("SELECT رقم_العهدة FROM المقاولات_العهد WHERE id = %s", (self.source_custody_id,))
                source_custody_number = cursor.fetchone()[0]

                payment_description = f"مبلغ مرحل من العهدة رقم {source_custody_number}"
                payment_type = "مبلغ_مرحل"

                cursor.execute("""
                    INSERT INTO دفعات_العهد
                    (معرف_العهدة, رقم_العهدة, وصف_الدفعة, المبلغ, تاريخ_الدفعة,
                     نوع_الدفعة, معرف_العهدة_المصدر, طريقة_الدفع, المستلم, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    new_custody_id,
                    self.new_custody_number_edit.text().strip(),
                    payment_description,
                    transfer_amount,
                    self.new_date_edit.date().toString("yyyy-MM-dd"),
                    payment_type,
                    self.source_custody_id,
                    "ترحيل",  # طريقة دفع
                    "",  # المستلم
                    f"مبلغ مرحل تلقائياً من العهدة رقم {source_custody_number}",
                    "admin",
                    datetime.now().year
                ))

            # إضافة دفعة إضافية إذا كان هناك مبلغ إضافي
            if additional_amount > 0:
                cursor.execute("""
                    INSERT INTO دفعات_العهد
                    (معرف_العهدة, رقم_العهدة, وصف_الدفعة, المبلغ, تاريخ_الدفعة,
                     نوع_الدفعة, طريقة_الدفع, المستلم, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    new_custody_id,
                    self.new_custody_number_edit.text().strip(),
                    "دفعة إضافية مع الترحيل",
                    additional_amount,
                    self.new_date_edit.date().toString("yyyy-MM-dd"),
                    "دفعة_إضافية",
                    "نقدي",  # طريقة دفع افتراضية
                    "",  # المستلم
                    "دفعة إضافية تم إضافتها مع عملية الترحيل",
                    "admin",
                    datetime.now().year
                ))

            # تحديث حالة العهدة المصدر إلى "مرحلة"
            cursor.execute("""
                UPDATE المقاولات_العهد
                SET حالة_العهدة = 'مرحلة', تاريخ_الإغلاق = %s
                WHERE id = %s
            """, (date.today(), self.source_custody_id))

            conn.commit()

            QMessageBox.information(self, "نجح", "تم ترحيل العهدة بنجاح")
            self.accept()

        except mysql.connector.IntegrityError:
            QMessageBox.warning(self, "خطأ", "رقم العهدة الجديدة موجود بالفعل")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في ترحيل العهدة: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()


class PaymentDialog(QDialog):
    """حوار إضافة/تعديل دفعة العهدة"""

    def __init__(self, parent=None, custody_id=None, payment_id=None):
        super().__init__(parent)
        self.custody_id = custody_id
        self.payment_id = payment_id
        self.is_edit_mode = payment_id is not None

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_payment_data()

        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل الدفعة" if self.is_edit_mode else "إضافة دفعة جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # نموذج الدفعة
        form_layout = QFormLayout()

        # وصف الدفعة
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("أدخل وصف الدفعة...")
        form_layout.addRow("وصف الدفعة:", self.description_edit)

        # مبلغ الدفعة
        amount_layout = QVBoxLayout()
        self.amount_edit = QLineEdit()
        # السماح بالأرقام السالبة والموجبة
        self.amount_edit.setValidator(QDoubleValidator(-999999999.99, 999999999.99, 2))
        self.amount_edit.setToolTip("يمكن إدخال قيم سالبة في حالة الخصم أو التصحيح")
        amount_layout.addWidget(self.amount_edit)

        # تلميح للمستخدم
        amount_hint = QLabel("💡 يمكن إدخال قيم سالبة للخصم أو التصحيح")
        amount_hint.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        amount_layout.addWidget(amount_hint)

        form_layout.addRow("مبلغ الدفعة:", amount_layout)

        # تاريخ الدفعة
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الدفعة:", self.date_edit)

        # نوع الدفعة
        self.payment_type_combo = QComboBox()
        self.payment_type_combo.addItems(["دفعة_إضافية", "دفعة_أولى", "مبلغ_مرحل"])
        form_layout.addRow("نوع الدفعة:", self.payment_type_combo)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([
            "نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"
        ])
        form_layout.addRow("طريقة الدفع:", self.payment_method_combo)

        # المستلم
        self.recipient_edit = QLineEdit()
        self.recipient_edit.setPlaceholderText("اسم المستلم...")
        form_layout.addRow("المستلم:", self.recipient_edit)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton(qta.icon('fa5s.save', color='green'), "حفظ")
        save_btn.setMinimumSize(100, 40)
        save_btn.clicked.connect(self.save_payment)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton(qta.icon('fa5s.times', color='red'), "إلغاء")
        cancel_btn.setMinimumSize(100, 40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

    def load_payment_data(self):
        """تحميل بيانات الدفعة للتعديل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT وصف_الدفعة, المبلغ, تاريخ_الدفعة, نوع_الدفعة,
                       طريقة_الدفع, المستلم, ملاحظات
                FROM دفعات_العهد
                WHERE id = %s
            """, (self.payment_id,))

            result = cursor.fetchone()
            if result:
                self.description_edit.setText(result[0] or "")
                self.amount_edit.setText(str(result[1] or 0))

                if result[2]:
                    self.date_edit.setDate(QDate.fromString(str(result[2]), "yyyy-MM-dd"))

                # تحديد نوع الدفعة
                payment_type = result[3] or "دفعة_إضافية"
                index = self.payment_type_combo.findText(payment_type)
                if index >= 0:
                    self.payment_type_combo.setCurrentIndex(index)

                # تحديد طريقة الدفع
                payment_method = result[4] or "نقدي"
                index = self.payment_method_combo.findText(payment_method)
                if index >= 0:
                    self.payment_method_combo.setCurrentIndex(index)

                self.recipient_edit.setText(result[5] or "")
                self.notes_edit.setPlainText(result[6] or "")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات الدفعة: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def save_payment(self):
        """حفظ الدفعة"""
        # التحقق من صحة البيانات
        if not self.description_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال وصف الدفعة")
            return

        if not self.amount_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ الدفعة")
            return

        try:
            amount_value = float(self.amount_edit.text())
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # تأكيد للقيم السالبة
        if amount_value < 0:
            reply = QMessageBox.question(self, "تأكيد المبلغ السالب",
                                       f"المبلغ المدخل سالب ({amount_value:,.2f}).\n"
                                       "هذا يعني خصم أو تصحيح للدفعات.\n"
                                       "هل تريد المتابعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return

        try:
            conn = mysql.connector.connect(
                host=host, user=user, password=password,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # الحصول على رقم العهدة
            cursor.execute("SELECT رقم_العهدة FROM المقاولات_العهد WHERE id = %s", (self.custody_id,))
            custody_number = cursor.fetchone()[0]

            # بيانات الدفعة
            payment_data = (
                self.custody_id,
                custody_number,
                self.description_edit.text().strip(),
                float(self.amount_edit.text()),
                self.date_edit.date().toString("yyyy-MM-dd"),
                self.payment_type_combo.currentText(),
                self.payment_method_combo.currentText(),
                self.recipient_edit.text().strip(),
                self.notes_edit.toPlainText(),
                "admin",  # المستخدم
                datetime.now().year
            )

            if self.is_edit_mode:
                # تحديث الدفعة
                cursor.execute("""
                    UPDATE دفعات_العهد SET
                        معرف_العهدة = %s, رقم_العهدة = %s, وصف_الدفعة = %s, المبلغ = %s,
                        تاريخ_الدفعة = %s, نوع_الدفعة = %s, طريقة_الدفع = %s, المستلم = %s,
                        ملاحظات = %s, السنة = %s
                    WHERE id = %s
                """, payment_data[:-1] + (self.payment_id,))
            else:
                # إضافة دفعة جديدة
                cursor.execute("""
                    INSERT INTO دفعات_العهد
                    (معرف_العهدة, رقم_العهدة, وصف_الدفعة, المبلغ, تاريخ_الدفعة,
                     نوع_الدفعة, طريقة_الدفع, المستلم, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, payment_data)

                # الحصول على id الدفعة الجديدة
                payment_id = cursor.lastrowid

            conn.commit()

            # ===== الربط المحاسبي لدفعة العهدة =====
            if not self.is_edit_mode:  # فقط للدفعات الجديدة
                try:
                    # إنشاء نظام الربط المحاسبي
                    from تكامل_المحاسبة import AccountingIntegration
                    accounting = AccountingIntegration()

                    # الحصول على بيانات العهدة
                    cursor.execute("""
                        SELECT اسم_المشروع, اسم_العميل, نسبة_المكتب
                        FROM المقاولات_العهد
                        WHERE id = %s
                    """, (self.custody_id,))
                    custody_info = cursor.fetchone()

                    if custody_info:
                        # إعداد بيانات دفعة العهدة للنظام المحاسبي
                        custody_payment_data = {
                            'id': payment_id,
                            'رقم_العهدة': custody_number,
                            'معرف_المشروع': self.custody_id,
                            'اسم_المشروع': custody_info[0],
                            'اسم_العميل': custody_info[1],
                            'مبلغ_العهدة': float(self.amount_edit.text()),
                            'نسبة_المكتب': custody_info[2] or 0,
                            'تاريخ_الإنشاء': self.date_edit.date().toString("yyyy-MM-dd"),
                            'المستخدم': 'admin'
                        }

                        # تسجيل دفعة العهدة محاسبياً
                        success, message = accounting.record_custody_transaction(custody_payment_data)

                        if success:
                            print(f"تم تسجيل دفعة العهدة محاسبياً: {message}")
                        else:
                            print(f"خطأ في التسجيل المحاسبي: {message}")

                    accounting.close_connection()

                except Exception as e:
                    print(f"خطأ في الربط المحاسبي لدفعة العهدة: {e}")
                    # الاستمرار حتى لو فشل الربط المحاسبي
            # ===== نهاية الربط المحاسبي =====

            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} الدفعة بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الدفعة: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()